---
# =========================================================================
# DNS Management Automation v2 - Main Orchestration Playbook
# =========================================================================
# This playbook implements the six-phase lifecycle framework for DNS management
# operations with comprehensive error handling, logging, and security controls.
#
# Six-Phase Lifecycle:
# 1. Configuration - Environment setup and validation
# 2. Loading - Inventory and credential management
# 3. Execution - Core DNS operations
# 4. Error Handling - Rollback and recovery procedures
# 5. Reporting - Status updates and audit logging
# 6. Cleanup - Resource cleanup and session termination
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

- name: "DNS Management Automation v2 - Six-Phase Lifecycle"
  hosts: localhost
  gather_facts: true
  become: false

  vars:
    # Execution metadata
    execution_id: "{{ ansible_date_time.epoch }}"
    job_start_time: "{{ ansible_date_time.iso8601 }}"

    # Logging configuration following organizational standards
    log_file_name: "{{ tower_job_id | default('LOCAL') }}_{{ ansible_date_time.day }}{{ ansible_date_time.month }}{{ ansible_date_time.year }}_{{ var_sr_number | default('NOTICKET') }}_DNS_ANSIBLE.log"
    log_directory: "C:\\OE_AAP_LOGS"

    # Phase tracking
    current_phase: "initialization"
    phases_completed: []
    rollback_required: false

    # Security and compliance
    security_context:
      user: "{{ ansible_user_id }}"
      job_id: "{{ tower_job_id | default('LOCAL_EXECUTION') }}"
      ticket: "{{ var_sr_number | default('NO_TICKET') }}"
      operation: "{{ var_action | default('verify') }}"

  vars_files:
    - group_vars/all/main.yml
    - group_vars/{{ var_environment | default('production') }}/main.yml

  handlers:
    - name: "Emergency Rollback Handler"
      include_tasks: roles/dns_lifecycle/handlers/emergency_rollback.yml
      when: rollback_required | bool

    - name: "Update Service Request"
      include_tasks: roles/audit_logging/handlers/update_service_request.yml

    - name: "Send Notification"
      include_tasks: roles/monitoring_integration/handlers/send_notification.yml

  pre_tasks:
    - name: "Initialize Execution Context"
      set_fact:
        execution_context:
          id: "{{ execution_id }}"
          start_time: "{{ job_start_time }}"
          user: "{{ ansible_user_id }}"
          job_id: "{{ tower_job_id | default('LOCAL') }}"
          ticket: "{{ var_sr_number | default('NO_TICKET') }}"
          operation: "{{ var_action | default('verify') }}"
          environment: "{{ var_environment | default('production') }}"
          log_file: "{{ log_file_name }}"
      tags: always

    - name: "Validate Required Variables"
      assert:
        that:
          - var_action is defined
          - domain is defined
          - hostname is defined
          - var_action in ['verify', 'add', 'remove', 'update']
        fail_msg: "Required variables missing or invalid. Required: var_action, domain, hostname"
        success_msg: "Required variables validated successfully"
      tags: always

    - name: "Initialize Audit Log"
      include_role:
        name: audit_logging
        tasks_from: initialize_audit_log
      tags: always

  tasks:
    # =====================================================================
    # PHASE 1: CONFIGURATION
    # =====================================================================
    - name: "Phase 1: Configuration - Environment Setup and Validation"
      block:
        - name: "Set Current Phase"
          set_fact:
            current_phase: "configuration"

        - name: "Configuration Phase - Environment Setup"
          include_role:
            name: dns_lifecycle
            tasks_from: phase_1_configuration
          vars:
            phase_name: "configuration"
            phase_description: "Environment setup and validation"

        - name: "Mark Configuration Phase Complete"
          set_fact:
            phases_completed: "{{ phases_completed + ['configuration'] }}"

      rescue:
        - name: "Configuration Phase Error Handler"
          include_role:
            name: dns_lifecycle
            tasks_from: phase_error_handler
          vars:
            failed_phase: "configuration"
            error_message: "{{ ansible_failed_result.msg | default('Unknown error in configuration phase') }}"

        - name: "Trigger Emergency Rollback"
          set_fact:
            rollback_required: true

        - name: "Fail Playbook Execution"
          fail:
            msg: "DNS Management failed in Configuration Phase: {{ ansible_failed_result.msg | default('Unknown error') }}"

      tags: ['phase1', 'configuration']

    # =====================================================================
    # PHASE 2: LOADING
    # =====================================================================
    - name: "Phase 2: Loading - Inventory and Credential Management"
      block:
        - name: "Set Current Phase"
          set_fact:
            current_phase: "loading"

        - name: "Loading Phase - Inventory and Credentials"
          include_role:
            name: dns_lifecycle
            tasks_from: phase_2_loading
          vars:
            phase_name: "loading"
            phase_description: "Inventory discovery and credential management"

        - name: "JIRA Integration Validation"
          include_tasks: roles/audit_logging/tasks/validate_jira_integration.yml
          when:
            - itsm_integration.enabled | default(false) | bool
            - jira_testing.validate_connectivity | default(true) | bool

        - name: "Mark Loading Phase Complete"
          set_fact:
            phases_completed: "{{ phases_completed + ['loading'] }}"

      rescue:
        - name: "Loading Phase Error Handler"
          include_role:
            name: dns_lifecycle
            tasks_from: phase_error_handler
          vars:
            failed_phase: "loading"
            error_message: "{{ ansible_failed_result.msg | default('Unknown error in loading phase') }}"

        - name: "Trigger Emergency Rollback"
          set_fact:
            rollback_required: true

        - name: "Fail Playbook Execution"
          fail:
            msg: "DNS Management failed in Loading Phase: {{ ansible_failed_result.msg | default('Unknown error') }}"

      tags: ['phase2', 'loading']

    # =====================================================================
    # PHASE 3: EXECUTION
    # =====================================================================
    - name: "Phase 3: Execution - Core DNS Operations"
      block:
        - name: "Set Current Phase"
          set_fact:
            current_phase: "execution"

        - name: "Execution Phase - DNS Operations"
          include_role:
            name: dns_lifecycle
            tasks_from: phase_3_execution
          vars:
            phase_name: "execution"
            phase_description: "Core DNS operations execution"

        - name: "Mark Execution Phase Complete"
          set_fact:
            phases_completed: "{{ phases_completed + ['execution'] }}"

      rescue:
        - name: "Execution Phase Error Handler"
          include_role:
            name: dns_lifecycle
            tasks_from: phase_error_handler
          vars:
            failed_phase: "execution"
            error_message: "{{ ansible_failed_result.msg | default('Unknown error in execution phase') }}"

        - name: "Trigger Emergency Rollback"
          set_fact:
            rollback_required: true

        - name: "Fail Playbook Execution"
          fail:
            msg: "DNS Management failed in Execution Phase: {{ ansible_failed_result.msg | default('Unknown error') }}"

      tags: ['phase3', 'execution']

    # =====================================================================
    # PHASE 4: ERROR HANDLING (Conditional)
    # =====================================================================
    - name: "Phase 4: Error Handling - Rollback and Recovery"
      block:
        - name: "Set Current Phase"
          set_fact:
            current_phase: "error_handling"

        - name: "Error Handling Phase - Rollback Procedures"
          include_role:
            name: dns_lifecycle
            tasks_from: phase_4_error_handling
          vars:
            phase_name: "error_handling"
            phase_description: "Error handling and rollback procedures"

        - name: "Mark Error Handling Phase Complete"
          set_fact:
            phases_completed: "{{ phases_completed + ['error_handling'] }}"

      when: rollback_required | bool
      tags: ['phase4', 'error_handling']

    # =====================================================================
    # PHASE 5: REPORTING
    # =====================================================================
    - name: "Phase 5: Reporting - Status Updates and Audit Logging"
      block:
        - name: "Set Current Phase"
          set_fact:
            current_phase: "reporting"

        - name: "Reporting Phase - Status and Audit"
          include_role:
            name: dns_lifecycle
            tasks_from: phase_5_reporting
          vars:
            phase_name: "reporting"
            phase_description: "Status reporting and audit logging"

        - name: "Mark Reporting Phase Complete"
          set_fact:
            phases_completed: "{{ phases_completed + ['reporting'] }}"

      rescue:
        - name: "Reporting Phase Error Handler"
          debug:
            msg: "Warning: Reporting phase encountered errors but execution will continue"

      always:
        - name: "Trigger Service Request Update"
          debug:
            msg: "Triggering service request update handler"
          notify: "Update Service Request"

        - name: "Trigger Notification"
          debug:
            msg: "Triggering notification handler"
          notify: "Send Notification"

      tags: ['phase5', 'reporting']

  post_tasks:
    # =====================================================================
    # PHASE 6: CLEANUP
    # =====================================================================
    - name: "Phase 6: Cleanup - Resource Cleanup and Session Termination"
      block:
        - name: "Set Current Phase"
          set_fact:
            current_phase: "cleanup"

        - name: "Cleanup Phase - Resource Management"
          include_role:
            name: dns_lifecycle
            tasks_from: phase_6_cleanup
          vars:
            phase_name: "cleanup"
            phase_description: "Resource cleanup and session termination"

        - name: "Mark Cleanup Phase Complete"
          set_fact:
            phases_completed: "{{ phases_completed + ['cleanup'] }}"

        - name: "Final Execution Summary"
          debug:
            msg: |
              DNS Management Automation v2 Execution Summary:
              ================================================
              Execution ID: {{ execution_id }}
              Job ID: {{ tower_job_id | default('LOCAL') }}
              Operation: {{ var_action | default('verify') }}
              Domain: {{ domain }}
              Hostname: {{ hostname }}
              Start Time: {{ job_start_time }}
              End Time: {{ ansible_date_time.iso8601 }}
              Phases Completed: {{ phases_completed | join(', ') }}
              Status: {{ 'FAILED' if rollback_required else 'SUCCESS' }}
              Log File: {{ log_file_name }}

      rescue:
        - name: "Cleanup Phase Error Handler"
          debug:
            msg: "Warning: Cleanup phase encountered errors but will not fail the playbook"

      tags: ['phase6', 'cleanup', 'always']
