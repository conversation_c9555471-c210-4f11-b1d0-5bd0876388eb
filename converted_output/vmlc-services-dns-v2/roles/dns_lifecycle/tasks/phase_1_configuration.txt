---
# =========================================================================
# DNS Lifecycle - Phase 1: Configuration
# =========================================================================
# This phase handles environment setup, variable validation, and initial
# configuration for DNS management operations following OXAF patterns.
#
# Phase Objectives:
# - Environment setup and variable loading
# - Inventory validation and host discovery
# - Credential retrieval and validation
# - Configuration template processing
#
# Author: CES Operational Excellence Team
# Contributor: Muhammad <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

- name: "Phase 1 Configuration - Start"
  debug:
    msg: |
      Starting DNS Lifecycle Phase 1: Configuration
      ============================================
      Phase: {{ phase_name }}
      Description: {{ phase_description }}
      Execution ID: {{ execution_context.id }}
      Job ID: {{ execution_context.job_id }}
      Operation: {{ execution_context.operation }}
      Domain: {{ domain }}
      Hostname: {{ hostname }}

- name: "Phase 1.1 - Environment Validation"
  block:
    - name: "Validate Execution Environment"
      assert:
        that:
          - ansible_version.major >= 2
          - ansible_version.minor >= 12
        fail_msg: "Ansible version 2.12 or higher required"
        success_msg: "Ansible version validation passed"

    - name: "Validate Required Collections"
      debug:
        msg: "Validating required Ansible collections are available"

    - name: "Check Windows Collection Availability"
      ansible.builtin.set_fact:
        windows_collection_available: true
      when: ansible_facts['os_family'] == "Windows" or inventory_hostname in groups['windows'] | default([])

    - name: "Validate DNS Operation Parameters"
      assert:
        that:
          - action in dns_operations.supported_actions
          - domain is defined and domain | length > 0
          - hostname is defined and hostname | length > 0
          - hostname | regex_search(dns_operations.validation.hostname_pattern)
        fail_msg: "Invalid DNS operation parameters"
        success_msg: "DNS operation parameters validated"

    - name: "Validate IP Address for Add Operations"
      assert:
        that:
          - ipaddress is defined
          - ipaddress | length > 0
          - ipaddress | ansible.utils.ipaddr
        fail_msg: "Valid IP address required for add operations"
        success_msg: "IP address validation passed"
      when: action == 'add'

  rescue:
    - name: "Environment Validation Failed"
      fail:
        msg: "Phase 1 Configuration failed during environment validation: {{ ansible_failed_result.msg }}"

- name: "Phase 1.2 - Domain and Server Configuration"
  block:
    - name: "Validate Domain Configuration"
      assert:
        that:
          - domain in dns_domain_mapping.keys()
        fail_msg: "Domain '{{ domain }}' not found in DNS domain mapping"
        success_msg: "Domain '{{ domain }}' found in configuration"

    - name: "Set Domain Configuration"
      set_fact:
        dns_domain_config: "{{ dns_domain_mapping[domain] }}"

    - name: "Set Primary DNS Servers"
      set_fact:
        dns_primary_servers: "{{ dns_domain_config.primary_servers }}"
        dns_backup_servers: "{{ dns_domain_config.backup_servers | default([]) }}"
        var_environment: "{{ dns_domain_config.environment }}"
        dns_security_zone: "{{ dns_domain_config.security_zone }}"

    - name: "Validate DNS Server Configuration"
      assert:
        that:
          - dns_primary_servers | length > 0
        fail_msg: "No primary DNS servers configured for domain '{{ domain }}'"
        success_msg: "DNS server configuration validated"

    - name: "Log Domain Configuration"
      debug:
        msg: |
          Domain Configuration Summary:
          ============================
          Domain: {{ domain }}
          Environment: {{ var_environment }}
          Security Zone: {{ dns_security_zone }}
          Primary Servers: {{ dns_primary_servers | join(', ') }}
          Backup Servers: {{ dns_backup_servers | join(', ') if dns_backup_servers | length > 0 else 'None' }}

  rescue:
    - name: "Domain Configuration Failed"
      fail:
        msg: "Phase 1 Configuration failed during domain configuration: {{ ansible_failed_result.msg }}"

- name: "Phase 1.3 - Credential Configuration"
  block:
    - name: "Validate Credential Mapping"
      assert:
        that:
          - domain in dns_credential_mapping.keys()
        fail_msg: "No credential mapping found for domain '{{ domain }}'"
        success_msg: "Credential mapping found for domain '{{ domain }}'"

    - name: "Set Credential Configuration"
      set_fact:
        dns_credential_username: "{{ dns_credential_mapping[domain] }}"

    - name: "Validate Credential Configuration"
      assert:
        that:
          - dns_credential_username is defined
          - dns_credential_username | length > 0
        fail_msg: "Invalid credential configuration for domain '{{ domain }}'"
        success_msg: "Credential configuration validated"

    - name: "Log Credential Configuration (Masked)"
      debug:
        msg: |
          Credential Configuration:
          ========================
          Domain: {{ domain }}
          Username: {{ dns_credential_username | regex_replace('(.{3}).*(.{3})', '\\1***\\2') }}
          Credential Source: CyberArk

  rescue:
    - name: "Credential Configuration Failed"
      fail:
        msg: "Phase 1 Configuration failed during credential configuration: {{ ansible_failed_result.msg }}"

- name: "Phase 1.4 - Logging Configuration"
  block:
    - name: "Set Logging Configuration"
      set_fact:
        dns_log_config:
          enabled: "{{ dns_logging.enabled }}"
          level: "{{ dns_logging.level }}"
          format: "{{ dns_logging.format }}"
          file_name: "{{ dns_logging.file_naming.pattern }}"
          directory: "{{ dns_logging.file_naming.directory }}"
          audit_enabled: "{{ dns_logging.audit.enabled }}"

    - name: "Create Log Directory Structure"
      ansible.windows.win_file:
        path: "{{ dns_log_config.directory }}"
        state: directory
      delegate_to: "{{ dns_primary_servers[0] }}"
      when: dns_log_config.enabled | bool

    - name: "Initialize Log File"
      ansible.builtin.lineinfile:
        path: "{{ dns_log_config.directory }}\\{{ dns_log_config.file_name }}"
        line: "{{ ansible_date_time.iso8601 }} [INFO] DNS Management Automation v2 - Execution Started - ID: {{ execution_context.id }} - Job: {{ execution_context.job_id }} - Operation: {{ execution_context.operation }} - Domain: {{ domain }} - Hostname: {{ hostname }} - User: {{ execution_context.user }} - Ticket: {{ execution_context.ticket }}"
        create: true
        insertafter: BOF
      delegate_to: "{{ dns_primary_servers[0] }}"
      when: dns_log_config.enabled | bool

    - name: "Log Configuration Summary"
      debug:
        msg: |
          Logging Configuration:
          =====================
          Enabled: {{ dns_log_config.enabled }}
          Level: {{ dns_log_config.level }}
          Format: {{ dns_log_config.format }}
          Log File: {{ dns_log_config.directory }}\\{{ dns_log_config.file_name }}
          Audit Enabled: {{ dns_log_config.audit_enabled }}

  rescue:
    - name: "Logging Configuration Failed"
      debug:
        msg: "Warning: Logging configuration failed but execution will continue: {{ ansible_failed_result.msg }}"

- name: "Phase 1.5 - Security Configuration"
  block:
    - name: "Set Security Configuration"
      set_fact:
        dns_security_config:
          encryption_in_transit: "{{ dns_security.encryption.in_transit }}"
          encryption_at_rest: "{{ dns_security.encryption.at_rest }}"
          rbac_enabled: "{{ dns_security.access_control.rbac_enabled }}"
          audit_all_access: "{{ dns_security.access_control.audit_all_access }}"
          compliance_hipaa: "{{ dns_security.compliance.hipaa }}"

    - name: "Validate Security Requirements"
      assert:
        that:
          - dns_security_config.encryption_in_transit | bool
          - dns_security_config.rbac_enabled | bool
        fail_msg: "Security requirements not met"
        success_msg: "Security configuration validated"

    - name: "Log Security Configuration"
      debug:
        msg: |
          Security Configuration:
          ======================
          Encryption in Transit: {{ dns_security_config.encryption_in_transit }}
          RBAC Enabled: {{ dns_security_config.rbac_enabled }}
          Audit All Access: {{ dns_security_config.audit_all_access }}
          HIPAA Compliance: {{ dns_security_config.compliance_hipaa }}

  rescue:
    - name: "Security Configuration Failed"
      fail:
        msg: "Phase 1 Configuration failed during security configuration: {{ ansible_failed_result.msg }}"

- name: "Phase 1.6 - Performance Configuration"
  block:
    - name: "Set Performance Configuration"
      set_fact:
        dns_performance_config:
          connection_timeout: "{{ dns_performance.timeouts.connection }}"
          operation_timeout: "{{ dns_performance.timeouts.operation }}"
          total_timeout: "{{ dns_performance.timeouts.total }}"
          max_parallel_operations: "{{ dns_performance.concurrency.max_parallel_operations }}"
          batch_size: "{{ dns_performance.concurrency.batch_size }}"

    - name: "Log Performance Configuration"
      debug:
        msg: |
          Performance Configuration:
          =========================
          Connection Timeout: {{ dns_performance_config.connection_timeout }}s
          Operation Timeout: {{ dns_performance_config.operation_timeout }}s
          Total Timeout: {{ dns_performance_config.total_timeout }}s
          Max Parallel Operations: {{ dns_performance_config.max_parallel_operations }}
          Batch Size: {{ dns_performance_config.batch_size }}

- name: "Phase 1 Configuration - Complete"
  debug:
    msg: |
      DNS Lifecycle Phase 1: Configuration - COMPLETED
      ===============================================
      Status: SUCCESS
      Domain: {{ domain }}
      Primary Servers: {{ dns_primary_servers | join(', ') }}
      Environment: {{ var_environment }}
      Security Zone: {{ dns_security_zone }}
      Log File: {{ dns_log_config.directory }}\\{{ dns_log_config.file_name }}
      Next Phase: Loading
