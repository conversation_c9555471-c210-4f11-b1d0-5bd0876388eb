# =========================================================================
# Ansible Vault - Encrypted Credentials for DNS Management Automation v2
# =========================================================================
# This file contains encrypted credentials and sensitive configuration data
# All values should be encrypted using ansible-vault encrypt_string command
#
# Framework: Operational Excellence Automation Framework (OXAF)
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

# =========================
# JIRA Production Credentials
# =========================

# Production JIRA service account credentials
# To encrypt: ansible-vault encrypt_string 'actual_username' --name 'vault_jira_prod_username'
vault_jira_prod_username: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# Production JIRA service account password
# To encrypt: ansible-vault encrypt_string 'actual_password' --name 'vault_jira_prod_password'
vault_jira_prod_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# Production JIRA Grid API token
# To encrypt: ansible-vault encrypt_string 'actual_grid_token' --name 'vault_jira_prod_grid_token'
vault_jira_prod_grid_token: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# =========================
# JIRA UAT/Development Credentials
# =========================

# UAT JIRA service account credentials (used by staging and development)
# To encrypt: ansible-vault encrypt_string 'actual_uat_username' --name 'vault_jira_uat_username'
vault_jira_uat_username: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# UAT JIRA service account password
# To encrypt: ansible-vault encrypt_string 'actual_uat_password' --name 'vault_jira_uat_password'
vault_jira_uat_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# UAT JIRA Grid API token
# To encrypt: ansible-vault encrypt_string 'actual_uat_grid_token' --name 'vault_jira_uat_grid_token'
vault_jira_uat_grid_token: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# =========================
# DNS Service Account Credentials
# =========================

# DNS service account credentials for domain operations
# To encrypt: ansible-vault encrypt_string 'actual_dns_username' --name 'vault_dns_service_username'
vault_dns_service_username: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# DNS service account password
# To encrypt: ansible-vault encrypt_string 'actual_dns_password' --name 'vault_dns_service_password'
vault_dns_service_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# =========================
# CyberArk Integration Credentials
# =========================

# CyberArk API credentials for credential retrieval
# To encrypt: ansible-vault encrypt_string 'actual_cyberark_username' --name 'vault_cyberark_username'
vault_cyberark_username: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# CyberArk API password
# To encrypt: ansible-vault encrypt_string 'actual_cyberark_password' --name 'vault_cyberark_password'
vault_cyberark_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# CyberArk API token
# To encrypt: ansible-vault encrypt_string 'actual_cyberark_token' --name 'vault_cyberark_api_token'
vault_cyberark_api_token: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# =========================
# Notification Service Credentials
# =========================

# Email service credentials for notifications
# To encrypt: ansible-vault encrypt_string 'actual_email_username' --name 'vault_email_username'
vault_email_username: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# Email service password
# To encrypt: ansible-vault encrypt_string 'actual_email_password' --name 'vault_email_password'
vault_email_password: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# =========================
# Monitoring Integration Credentials
# =========================

# Splunk integration credentials
# To encrypt: ansible-vault encrypt_string 'actual_splunk_token' --name 'vault_splunk_token'
vault_splunk_token: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# Prometheus integration credentials
# To encrypt: ansible-vault encrypt_string 'actual_prometheus_token' --name 'vault_prometheus_token'
vault_prometheus_token: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# =========================
# Security and Compliance Credentials
# =========================

# Security scanning service credentials
# To encrypt: ansible-vault encrypt_string 'actual_security_token' --name 'vault_security_scanner_token'
vault_security_scanner_token: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834

# Compliance reporting service credentials
# To encrypt: ansible-vault encrypt_string 'actual_compliance_token' --name 'vault_compliance_token'
vault_compliance_token: !vault |
          $ANSIBLE_VAULT;1.1;AES256
          66386439653966336464623437323964393064386137343834376464323839656266643264363831
          3438373936396665653936363834613763383936316664650a666433346366323462626263326464
          39653663323464643665386439623966323634663936396634653834643235656464376431666564
          3833666462613037650a316464663834643834373834373834373834373834373834373834373834
          3834373834373834373834373834373834373834373834373834373834373834373834373834
