# DNS Management Automation v2 - Installation Guide

## Overview

This installation guide provides step-by-step instructions for deploying the DNS Management Automation v2 solution in your Ansible Automation Platform (AAP) environment.

**Author:** CES Operational Excellence Team
**Contributor:** <PERSON> (7409)
**Framework:** Operational Excellence Automation Framework (OXAF)
**Version:** 2.0

## Prerequisites

### System Requirements

#### Ansible Automation Platform
- **Version:** AAP 2.x or higher
- **Controller:** Ansible Controller with sufficient resources
- **Execution Environment:** Custom EE with required collections
- **Storage:** Minimum 10GB for logs and temporary files

#### Target DNS Servers
- **Operating System:** Windows Server 2019/2022
- **PowerShell:** Version 5.1 or higher
- **WinRM:** Enabled and configured
- **DNS Role:** DNS Server role installed and configured

#### Network Requirements
- **Connectivity:** AAP Controller to DNS servers on ports 5985/5986
- **Firewall:** Appropriate firewall rules configured
- **DNS Resolution:** Proper DNS resolution between systems

### Access Requirements

#### Credentials and Authentication
- **CyberArk Access:** Valid CyberArk credentials for DNS service accounts
- **AAP Permissions:** Appropriate RBAC permissions in AAP
- **DNS Permissions:** Service accounts with DNS management permissions

#### Service Accounts
- Domain-specific service accounts stored in CyberArk
- Accounts must have DNS management permissions
- Regular credential rotation schedule established

## Installation Steps

### Step 1: Prepare AAP Environment

#### 1.1 Create Project in AAP

1. **Navigate to Projects** in AAP Controller
2. **Create New Project** with the following settings:
   ```yaml
   Name: DNS Management v2
   Description: DNS Management Automation v2 - OXAF Framework
   Organization: Your Organization
   SCM Type: Git
   SCM URL: [Your Git Repository URL]
   SCM Branch: main
   SCM Update Options:
     - Clean
     - Delete on Update
     - Update Revision on Launch
   ```

3. **Sync Project** to ensure all files are available

#### 1.2 Configure Execution Environment

1. **Create Custom Execution Environment** (if not exists):
   ```dockerfile
   FROM quay.io/ansible/ansible-runner:latest

   # Install required collections
   RUN ansible-galaxy collection install ansible.windows
   RUN ansible-galaxy collection install community.general
   RUN ansible-galaxy collection install ansible.posix
   RUN ansible-galaxy collection install cloud_cpe.cyberark_ccp

   # Install additional Python packages
   RUN pip install pywinrm
   RUN pip install requests
   ```

2. **Register Execution Environment** in AAP Controller

### Step 2: Configure Credentials

#### 2.1 CyberArk Credential Provider

1. **Create CyberArk Credential** in AAP:
   ```yaml
   Name: CyberArk DNS Credential Provider
   Type: CyberArk CCP
   CyberArk CCP URL: [Your CyberArk URL]
   Application ID: ANSIBLE_DNS
   Client Certificate: [Certificate if required]
   Client Key: [Key if required]
   Verify SSL: true
   ```

#### 2.2 DNS Service Account Credentials

1. **Configure DNS Service Accounts** in CyberArk:
   ```yaml
   Safe: DNS_AUTOMATION
   Accounts:
     - var_dns_healthgrp_username
     - var_dns_nhg_username
     - var_dns_aic_username
     - var_dns_iltc_username
     - var_dns_shses_username
     - [Additional domain accounts]
   ```

### Step 3: Configure Inventory

#### 3.1 Create DNS Servers Inventory

1. **Create Inventory** in AAP:
   ```yaml
   Name: DNS Servers - Production
   Description: Production DNS servers for DNS Management v2
   Organization: Your Organization
   ```

2. **Import Inventory** from project:
   ```yaml
   Source: Sourced from a Project
   Project: DNS Management v2
   Inventory File: inventory/production/hosts.yml
   Update Options:
     - Overwrite
     - Update on Launch
   ```

#### 3.2 Validate Inventory

1. **Sync Inventory** to load hosts
2. **Verify Host Variables** are properly configured
3. **Test Connectivity** to DNS servers

### Step 4: Create Job Templates

#### 4.1 DNS Record Verification Template

```yaml
Name: DNS Record Verification - Production
Job Type: Run
Inventory: DNS Servers - Production
Project: DNS Management v2
Playbook: main.yml
Credentials:
  - CyberArk DNS Credential Provider
Execution Environment: [Your Custom EE]
Variables:
  environment: production
  action: verify
Survey Enabled: Yes
Survey Specification: [See Survey Configuration]
```

#### 4.2 DNS Record Addition Template

```yaml
Name: DNS Record Addition - Production
Job Type: Run
Inventory: DNS Servers - Production
Project: DNS Management v2
Playbook: main.yml
Credentials:
  - CyberArk DNS Credential Provider
Execution Environment: [Your Custom EE]
Variables:
  environment: production
  action: add
Survey Enabled: Yes
Survey Specification: [See Survey Configuration]
```

#### 4.3 DNS Record Removal Template

```yaml
Name: DNS Record Removal - Production
Job Type: Run
Inventory: DNS Servers - Production
Project: DNS Management v2
Playbook: main.yml
Credentials:
  - CyberArk DNS Credential Provider
Execution Environment: [Your Custom EE]
Variables:
  environment: production
  action: remove
Survey Enabled: Yes
Survey Specification: [See Survey Configuration]
```

### Step 5: Configure Survey Specifications

#### 5.1 Common Survey Fields

```yaml
- variable: domain
  question: "DNS Domain"
  type: multiplechoice
  choices:
    - healthgrp.com.sg
    - nhg.local
    - aic.local
    - iltc.healthgrp.com.sg
    - shses.shs.com.sg
  required: true

- variable: hostname
  question: "Hostname (without domain)"
  type: text
  required: true
  min: 1
  max: 63

- variable: var_sr_number
  question: "Service Request Number"
  type: text
  required: true
  default: "SR-"

- variable: testing_mode
  question: "Enable Testing Mode"
  type: boolean
  required: false
  default: false
```

#### 5.2 Add Operation Additional Fields

```yaml
- variable: ipaddress
  question: "IP Address"
  type: text
  required: true

- variable: ttl
  question: "TTL (Time To Live) in seconds"
  type: integer
  required: false
  default: 3600
  min: 60
  max: 86400
```

### Step 6: Configure Logging and Monitoring

#### 6.1 Log Directory Setup

1. **Create Log Directories** on DNS servers:
   ```powershell
   New-Item -Path "C:\OE_AAP_LOGS" -ItemType Directory -Force
   New-Item -Path "C:\OE_AAP_LOGS\PRODUCTION" -ItemType Directory -Force
   New-Item -Path "C:\OE_AAP_LOGS\PRODUCTION\backup" -ItemType Directory -Force
   New-Item -Path "C:\OE_AAP_LOGS\PRODUCTION\archive" -ItemType Directory -Force
   ```

2. **Set Permissions** on log directories:
   ```powershell
   # Grant appropriate permissions to service accounts
   icacls "C:\OE_AAP_LOGS" /grant "DNS_SERVICE_ACCOUNT:(OI)(CI)F"
   ```

#### 6.2 Monitoring Integration

1. **Configure SIEM Integration** (if applicable):
   - Set up log forwarding to Splunk/ELK
   - Configure real-time alerting
   - Set up dashboard monitoring

2. **Configure Performance Monitoring**:
   - Set up metrics collection
   - Configure threshold alerting
   - Set up performance dashboards

### Step 7: Testing and Validation

#### 7.1 Molecule Testing

1. **Install Molecule** (if not available):
   ```bash
   pip install molecule[docker]
   pip install molecule-docker
   ```

2. **Run Molecule Tests**:
   ```bash
   cd vmlc-services-dns-v2
   molecule test
   ```

#### 7.2 Integration Testing

1. **Test DNS Record Verification**:
   - Run verification job template
   - Verify log output and formatting
   - Confirm audit trail creation

2. **Test DNS Record Addition** (in test environment):
   - Run addition job template with test data
   - Verify DNS record creation
   - Test rollback functionality

3. **Test Error Handling**:
   - Simulate error conditions
   - Verify rollback procedures
   - Test notification systems

### Step 8: Production Deployment

#### 8.1 Pre-deployment Checklist

- [ ] All prerequisites met
- [ ] Credentials configured and tested
- [ ] Inventory validated
- [ ] Job templates created and tested
- [ ] Logging directories created
- [ ] Monitoring configured
- [ ] Testing completed successfully
- [ ] Documentation reviewed
- [ ] Team training completed

#### 8.2 Deployment Steps

1. **Deploy to Production**:
   - Sync project in production AAP
   - Validate all configurations
   - Test connectivity to production DNS servers

2. **Validate Production Deployment**:
   - Run verification job template
   - Check log file creation
   - Verify monitoring integration

#### 8.3 Post-deployment Validation

1. **Functional Testing**:
   - Test all job templates
   - Verify six-phase lifecycle execution
   - Confirm error handling and rollback

2. **Performance Testing**:
   - Monitor execution times
   - Check resource utilization
   - Validate performance metrics

3. **Security Testing**:
   - Verify credential security
   - Check audit log completeness
   - Validate compliance requirements

## Troubleshooting

### Common Installation Issues

#### Issue: Project Sync Fails
```
Solution:
1. Check Git repository access
2. Verify network connectivity
3. Check AAP project configuration
4. Review error logs in AAP
```

#### Issue: Credential Retrieval Fails
```
Solution:
1. Verify CyberArk connectivity
2. Check service account permissions
3. Validate CyberArk configuration
4. Test credential manually
```

#### Issue: DNS Server Connectivity Fails
```
Solution:
1. Check network connectivity (ports 5985/5986)
2. Verify WinRM configuration
3. Check firewall rules
4. Test manual WinRM connection
```

#### Issue: PowerShell Script Execution Fails
```
Solution:
1. Check PowerShell execution policy
2. Verify script permissions
3. Check DNS server permissions
4. Review PowerShell error logs
```

### Support and Escalation

#### Level 1 Support
- Review installation documentation
- Check common troubleshooting steps
- Verify configuration settings

#### Level 2 Support
- Contact CES Operational Excellence Team
- Provide detailed error logs
- Include environment configuration details

#### Level 3 Support
- Escalate to development team
- Provide complete diagnostic information
- Include reproduction steps

## Maintenance and Updates

### Regular Maintenance Tasks

1. **Weekly**:
   - Review log files and cleanup
   - Check system performance
   - Validate credential rotation

2. **Monthly**:
   - Update documentation
   - Review and update configurations
   - Performance optimization review

3. **Quarterly**:
   - Security assessment
   - Compliance review
   - Disaster recovery testing

### Update Procedures

1. **Minor Updates**:
   - Test in development environment
   - Deploy during maintenance window
   - Validate functionality post-update

2. **Major Updates**:
   - Full testing cycle
   - Change management approval
   - Rollback plan preparation
   - Staged deployment approach

---

## Support Information

### Documentation
- [User Guide](USER_GUIDE.md)
- [Configuration Guide](CONFIGURATION.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)
- [API Reference](API_REFERENCE.md)

### Support Contacts
- **Technical Support:** CES Operational Excellence Team
- **Emergency Support:** 24/7 on-call support
- **Training:** Available workshops and documentation

---

*This installation guide is part of the DNS Management Automation v2 solution developed by the CES Operational Excellence Team following Operational Excellence Automation Framework (OXAF) patterns.*
