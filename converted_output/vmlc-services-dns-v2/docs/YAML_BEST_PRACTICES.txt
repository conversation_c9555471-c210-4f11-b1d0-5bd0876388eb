# YAML Best Practices for DNS Management Automation v2

## Comprehensive Guide for AAP YAML Configuration

**Framework:** Operational Excellence Automation Framework (OXAF)  
**Version:** 2.0  
**Purpose:** Best practices and validation guide for YAML configurations  

---

## 📋 **YAML Formatting Standards**

### **Indentation Rules**
- ✅ **Use 2 spaces** for indentation (never tabs)
- ✅ **Consistent alignment** for nested elements
- ✅ **No trailing spaces** at end of lines
- ✅ **Empty line separation** between major sections

### **Good YAML Example**
```yaml
# DNS Record Addition - Production Web Server
var_action: add
domain: healthgrp.com.sg
hostname: web01
ipaddress: *************
ttl: 3600
var_sr_number: SR-WEB-001
testing_mode: false

# Execution context for audit trail
execution_context:
  purpose: Web server deployment
  team: Infrastructure Team
  operator: <EMAIL>
  deployment_date: "2024-01-15"
```

### **Bad YAML Examples to Avoid**
```yaml
# ❌ Bad - Inconsistent indentation
var_action: add
  domain: healthgrp.com.sg
    hostname: web01

# ❌ Bad - Using tabs instead of spaces
var_action: add
	domain: healthgrp.com.sg

# ❌ Bad - Incorrect boolean values
testing_mode: True  # Should be: true

# ❌ Bad - Missing quotes for special characters
hostname: server-01:8080  # Should be: "server-01:8080"
```

---

## 🔤 **Data Type Guidelines**

### **String Values**
```yaml
# Simple strings (no quotes needed)
hostname: webserver01
domain: healthgrp.com.sg

# Strings with special characters (quotes required)
hostname: "web-server:8080"
description: "Server with special chars: @#$%"

# Multi-line strings
description: |
  This is a multi-line description
  that spans multiple lines
  and preserves line breaks

# Folded strings (single line)
description: >
  This is a long description
  that will be folded into
  a single line
```

### **Numeric Values**
```yaml
# Integers
ttl: 3600
port: 80
timeout: 300

# Floating point
version: 2.0
weight: 0.5
```

### **Boolean Values**
```yaml
# Correct boolean values (lowercase)
testing_mode: true
enable_rollback: false
validate_before_execution: true

# ❌ Incorrect boolean values
testing_mode: True   # Should be: true
enable_rollback: FALSE  # Should be: false
```

### **Arrays/Lists**
```yaml
# Simple list
environments:
  - development
  - staging
  - production

# List of objects
servers:
  - hostname: web01
    ipaddress: *************
  - hostname: web02
    ipaddress: *************
```

---

## 📝 **Comment Best Practices**

### **Effective Use of Comments**
```yaml
# DNS Record Addition for Customer Portal
# Service Request: SR-WEB-001
# Deployment Date: 2024-01-15
var_action: add
domain: healthgrp.com.sg
hostname: web01
ipaddress: *************
ttl: 3600  # Standard production TTL (1 hour)
var_sr_number: SR-WEB-001
testing_mode: false  # Production deployment

# Execution context for audit and compliance
execution_context:
  purpose: Web server deployment
  team: Infrastructure Team
  
  # Change management references
  approval_reference: CHANGE-2024-001
  maintenance_window: "2024-01-15 02:00:00 SGT"
  
  # Technical specifications
  server_role: web_frontend
  application: customer_portal
  criticality: high  # High availability required
```

### **Comment Guidelines**
- 📝 **Document purpose** at the top of configuration
- 🔗 **Reference tickets** and change requests
- ⚠️ **Explain non-obvious values** (e.g., specific TTL choices)
- 🎯 **Clarify business context** for technical decisions
- 📅 **Include timestamps** for time-sensitive configurations

---

## 🔍 **YAML Validation Checklist**

### **Syntax Validation**
- ✅ **Indentation:** Consistent 2-space indentation
- ✅ **Colons:** Space after colons in key-value pairs
- ✅ **Dashes:** Space after dashes in lists
- ✅ **Quotes:** Proper quoting for special characters
- ✅ **Boolean:** Lowercase true/false values

### **Content Validation**
- ✅ **Required Fields:** All mandatory fields present
- ✅ **Data Types:** Correct data types for each field
- ✅ **IP Addresses:** Valid IPv4 format
- ✅ **Domain Names:** Valid FQDN format
- ✅ **Service Requests:** Proper SR number format

### **Validation Tools**

#### **Online Validators**
```
YAML Lint: http://www.yamllint.com/
Online YAML Parser: https://yaml-online-parser.appspot.com/
YAML Validator: https://codebeautify.org/yaml-validator
```

#### **Command Line Validation**
```bash
# Using Python
python -c "import yaml; print(yaml.safe_load(open('config.yaml')))"

# Using yq (if installed)
yq eval '.' config.yaml

# Using Ansible syntax check
ansible-playbook --syntax-check -e @config.yaml playbook.yml
```

---

## 🎯 **Template Library**

### **Basic Operations Templates**

#### **DNS Record Addition Template**
```yaml
# DNS Record Addition Template
# Replace placeholders with actual values
var_action: add
domain: DOMAIN_NAME  # e.g., healthgrp.com.sg
hostname: HOSTNAME   # e.g., web01
ipaddress: IP_ADDRESS  # e.g., *************
ttl: TTL_VALUE      # e.g., 3600 (1 hour)
var_sr_number: SR_NUMBER  # e.g., SR-WEB-001
testing_mode: false  # Set to true for testing

# Optional execution context
execution_context:
  purpose: PURPOSE_DESCRIPTION
  team: TEAM_NAME
  operator: OPERATOR_EMAIL
```

#### **DNS Record Verification Template**
```yaml
# DNS Record Verification Template
var_action: verify
domain: DOMAIN_NAME
hostname: HOSTNAME
var_sr_number: SR_NUMBER
testing_mode: false

# Optional environment override
var_environment: production  # development, staging, production
```

#### **DNS Record Update Template**
```yaml
# DNS Record Update Template
var_action: update
domain: DOMAIN_NAME
hostname: HOSTNAME
ipaddress: NEW_IP_ADDRESS
ttl: TTL_VALUE
var_sr_number: SR_NUMBER
testing_mode: false

# Recommended for production updates
enable_rollback: true
rollback_timeout: 300  # 5 minutes

execution_context:
  purpose: IP address migration
  maintenance_window: "YYYY-MM-DD HH:MM:SS SGT"
  approval_reference: CHANGE_NUMBER
```

### **Environment-Specific Templates**

#### **Development Environment Template**
```yaml
# Development Environment Template
var_action: add
domain: devhealthgrp.com.sg
hostname: HOSTNAME
ipaddress: IP_ADDRESS  # Use 10.x.x.x range
ttl: 300  # Short TTL for development
var_environment: development
testing_mode: true  # Always test in development
var_sr_number: SR-DEV-XXX

execution_context:
  purpose: Development testing
  team: Development Team
  test_phase: unit_testing
```

#### **Production Environment Template**
```yaml
# Production Environment Template
var_action: add
domain: healthgrp.com.sg
hostname: HOSTNAME
ipaddress: IP_ADDRESS
ttl: 3600  # Standard production TTL
var_environment: production
testing_mode: false
var_sr_number: SR-PROD-XXX

# Production safety features
enhanced_logging: true
audit_level: detailed
enable_rollback: true

execution_context:
  purpose: Production deployment
  team: Infrastructure Team
  operator: OPERATOR_EMAIL
  approval_reference: CHANGE_NUMBER
  maintenance_window: "YYYY-MM-DD HH:MM:SS SGT"
  criticality: high
```

---

## 🔄 **Complex Configuration Examples**

### **Bulk Server Deployment**
```yaml
# Bulk Web Server Deployment
var_action: add
ttl: 3600
var_sr_number: SR-BULK-WEB-001
testing_mode: false

# Multiple servers in single operation
servers:
  - domain: healthgrp.com.sg
    hostname: web01
    ipaddress: *************
  - domain: healthgrp.com.sg
    hostname: web02
    ipaddress: *************
  - domain: healthgrp.com.sg
    hostname: web03
    ipaddress: *************

execution_context:
  purpose: Web farm expansion
  team: Infrastructure Team
  deployment_batch: batch_1
  load_balancer_update_required: true
```

### **Multi-Environment Deployment**
```yaml
# Multi-Environment API Server Deployment
var_action: add
var_sr_number: SR-API-DEPLOY-001
testing_mode: false

# Environment-specific configurations
environments:
  development:
    domain: devhealthgrp.com.sg
    hostname: api01
    ipaddress: **********
    ttl: 300  # Short TTL for development
  staging:
    domain: stghealthgrp.com.sg
    hostname: api01
    ipaddress: ***************
    ttl: 1800  # Medium TTL for staging
  production:
    domain: healthgrp.com.sg
    hostname: api01
    ipaddress: ************
    ttl: 3600  # Standard production TTL

execution_context:
  purpose: API service deployment
  team: Application Team
  deployment_strategy: blue_green
  rollback_plan: available
```

### **Database Migration with Rollback**
```yaml
# Database Server Migration
var_action: update
domain: healthgrp.com.sg
hostname: db01
ipaddress: ************  # New data center IP
ttl: 7200  # Extended TTL for database
var_sr_number: SR-DB-MIGRATE-001
testing_mode: false

# Enhanced safety for critical system
enable_rollback: true
rollback_timeout: 600  # 10 minutes for database
validate_before_execution: true
enhanced_logging: true
audit_level: detailed

execution_context:
  purpose: Database server migration
  team: Database Team
  
  # Change management
  approval_reference: CHANGE-***********
  maintenance_window: "2024-01-15 02:00:00 SGT"
  
  # Technical details
  database_type: postgresql
  replication_impact: primary_replica
  backup_completed: true
  
  # Risk management
  criticality: critical
  business_impact: high
  rollback_plan: automated
  monitoring_alerts: enabled
```

---

## ⚠️ **Common Pitfalls and Solutions**

### **Indentation Issues**
```yaml
# ❌ Problem: Mixed indentation
var_action: add
domain: healthgrp.com.sg
  hostname: web01    # Wrong indentation
    ipaddress: *************  # Wrong indentation

# ✅ Solution: Consistent 2-space indentation
var_action: add
domain: healthgrp.com.sg
hostname: web01
ipaddress: *************
```

### **Boolean Value Issues**
```yaml
# ❌ Problem: Incorrect boolean format
testing_mode: True
enable_rollback: FALSE
validate: yes

# ✅ Solution: Use lowercase true/false
testing_mode: true
enable_rollback: false
validate: true
```

### **String Quoting Issues**
```yaml
# ❌ Problem: Unquoted special characters
hostname: web-01:8080
description: Server with 100% uptime

# ✅ Solution: Quote strings with special characters
hostname: "web-01:8080"
description: "Server with 100% uptime"
```

### **List Format Issues**
```yaml
# ❌ Problem: Incorrect list format
servers: web01, web02, web03

# ✅ Solution: Proper YAML list format
servers:
  - web01
  - web02
  - web03
```

---

## 🛠️ **Troubleshooting Guide**

### **Common Error Messages**

#### **"YAML syntax error"**
**Cause:** Invalid YAML syntax  
**Solution:** Check indentation, quotes, and special characters  
**Tool:** Use online YAML validator

#### **"Mapping values are not allowed here"**
**Cause:** Incorrect indentation or missing colon  
**Solution:** Verify key-value pair format and indentation

#### **"Found character that cannot start any token"**
**Cause:** Invalid character or encoding issue  
**Solution:** Check for hidden characters or encoding problems

### **Debugging Steps**
1. **Validate syntax** using online YAML validator
2. **Check indentation** - use 2 spaces consistently
3. **Verify quotes** around special characters
4. **Test with minimal** configuration first
5. **Add complexity** gradually

---

## 📚 **Additional Resources**

### **YAML Learning Resources**
- **YAML Official Specification:** https://yaml.org/spec/
- **YAML Tutorial:** https://learnxinyminutes.com/docs/yaml/
- **Ansible YAML Guide:** https://docs.ansible.com/ansible/latest/reference_appendices/YAMLSyntax.html

### **Validation Tools**
- **yamllint:** Command-line YAML linter
- **yq:** Command-line YAML processor
- **VS Code YAML Extension:** Real-time validation in editor

### **Best Practice References**
- **Ansible Best Practices:** https://docs.ansible.com/ansible/latest/user_guide/playbooks_best_practices.html
- **YAML Style Guide:** Various community style guides available

---

*This YAML best practices guide is part of the DNS Management Automation v2 solution developed by the CES Operational Excellence Team following Operational Excellence Automation Framework (OXAF) patterns.*
