# IaCOps Framework Usage Guide for Augment Code VS Code IDE

**Author:** CES Operational Excellence Team, Contributor: <PERSON> (7409)
**Version:** 1.0
**Date:** [Current Date]

## Table of Contents

1. [Overview](#overview)
2. [Setup and Installation](#setup-and-installation)
3. [Framework Structure](#framework-structure)
4. [Using the Orchestrator](#using-the-orchestrator)
5. [Infrastructure Personas](#infrastructure-personas)
6. [Task Execution](#task-execution)
7. [Best Practices](#best-practices)
8. [Troubleshooting](#troubleshooting)

## Overview

The IaCOps Framework is specifically designed to work seamlessly with Augment Code VS Code IDE, providing AI-driven infrastructure automation and design capabilities. This guide will teach you how to effectively use the framework for Infrastructure as Code operations in on-premise environments.

### Key Benefits with Augment Code
- **Context-Aware AI:** Augment Code's context engine understands your infrastructure codebase
- **Intelligent Code Completion:** AI-powered suggestions for Ansible playbooks and infrastructure code
- **Cross-Reference Navigation:** Easy navigation between related infrastructure components
- **Real-Time Collaboration:** AI agents work alongside your development workflow

## Setup and Installation

### Prerequisites
- VS Code with Augment Code extension installed
- Access to your infrastructure project repository
- Basic understanding of Infrastructure as Code concepts

### Installation Steps

1. **Copy Framework to Project:**
   ```bash
   # Navigate to your project root
   cd /path/to/your/infrastructure-project

   # Copy the bmad-agent folder
   cp -r /path/to/iacops-framework/bmad-agent ./
   ```

2. **Verify Framework Structure:**
   ```
   your-project/
   ├── bmad-agent/
   │   ├── personas/
   │   ├── tasks/
   │   ├── templates/
   │   ├── checklists/
   │   └── data/
   ├── docs/
   │   └── infrastructure/
   ├── ansible/
   ├── pipelines/
   └── monitoring/
   ```

3. **Configure Augment Code:**
   - Open VS Code in your project directory
   - Ensure Augment Code extension is active
   - The framework will be automatically indexed by Augment Code's context engine

## Framework Structure

### Core Components

#### Personas (`bmad-agent/personas/`)
- **Infrastructure Architect** (`infrastructure-architect.md`)
- **Platform Engineer** (`platform-engineer.md`)
- **Systems Engineer** (`systems-engineer.md`)
- **Automation Engineer** (`automation-engineer.md`)
- **DevOps Engineer** (`devops-engineer.md`)
- **Security Engineer** (`security-engineer.md`)
- **Operations Engineer** (`operations-engineer.md`)

#### Tasks (`bmad-agent/tasks/`)
- Infrastructure design and planning tasks
- Ansible playbook development tasks
- Security and compliance tasks
- Monitoring and operations tasks

#### Templates (`bmad-agent/templates/`)
- Infrastructure architecture templates
- Ansible playbook templates
- Documentation templates
- Security policy templates

#### Checklists (`bmad-agent/checklists/`)
- Infrastructure design validation
- Security compliance verification
- Quality assurance checkpoints

## Using the Orchestrator

### Starting the Orchestrator

1. **Open Augment Code Chat:**
   - Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
   - Type "Augment: Open Chat"
   - Or use the Augment Code chat panel

2. **Load the Orchestrator:**
   ```
   Load the IaCOps orchestrator from bmad-agent/ide-bmad-orchestrator.md and use the configuration from bmad-agent/ide-bmad-orchestrator-cfg.md
   ```

3. **Initialize Framework:**
   The orchestrator will load and present available infrastructure personas.

### Selecting a Persona

**Example Interaction:**
```
User: I need to design a new infrastructure architecture for a Red Hat OpenShift deployment

Orchestrator: I can help you with that! Based on your request, I recommend the Infrastructure Architect persona.

Available personas:
- Infrastructure Architect (Alex) - Strategic infrastructure design and capacity planning
- Platform Engineer (Jordan) - OpenShift and container orchestration expertise
- Systems Engineer (Sam) - Server and virtualization configuration

Which persona would you like me to become?

User: Infrastructure Architect

Orchestrator: Activating Infrastructure Architect (Alex)...
I'm now your Infrastructure Architect. I can help you design comprehensive infrastructure architectures.

Available tasks:
- Design Infrastructure Architecture
- Create Capacity Planning
- Design Backup Recovery

What would you like to work on?
```

## Infrastructure Personas

### Infrastructure Architect (Alex)
**Best for:**
- High-level infrastructure design
- Technology stack decisions
- Capacity planning and sizing
- Architecture documentation

**Example Usage:**
```
User: Design Infrastructure Architecture

Alex: I'll help you design a comprehensive infrastructure architecture. Let me start by analyzing your requirements...

[The agent will guide you through the infrastructure design process using the design-infrastructure-architecture.md task]
```

### Automation Engineer (Casey)
**Best for:**
- Ansible playbook development
- AAP workflow design
- Six-phase lifecycle implementation
- Automation testing and validation

**Example Usage:**
```
User: Create Ansible Playbook

Casey: I'll help you create a comprehensive Ansible playbook following our six-phase lifecycle framework. Let's start by analyzing your automation requirements...

[The agent will guide you through playbook development using the create-ansible-playbook.md task]
```

### Platform Engineer (Jordan)
**Best for:**
- Red Hat OpenShift configuration
- Container orchestration
- CI/CD pipeline design
- Platform automation

### Systems Engineer (Sam)
**Best for:**
- Windows/Linux server configuration
- VMware ESXi and Nutanix HCI setup
- Virtualization strategies
- System hardening

### Security Engineer (Morgan)
**Best for:**
- Security compliance validation
- Infrastructure hardening
- Security policy development
- Vulnerability assessment

### DevOps Engineer (Taylor)
**Best for:**
- End-to-end pipeline design
- Monitoring strategy development
- Incident response planning
- Operational excellence

### Operations Engineer (Riley)
**Best for:**
- Incident response procedures
- Troubleshooting guides
- Maintenance procedures
- Operational support

## Task Execution

### Working with Tasks

1. **Task Selection:**
   - Each persona has specific tasks they can perform
   - Tasks are loaded dynamically based on the persona's capabilities

2. **Task Execution:**
   - Tasks guide you through structured workflows
   - Follow the six-phase lifecycle where applicable
   - Provide comprehensive documentation and validation

3. **Task Outputs:**
   - Generated artifacts are saved to appropriate project locations
   - Documentation follows organizational standards
   - Quality gates ensure completeness and accuracy

### Example: Creating an Ansible Playbook

```
User: I need to create an Ansible playbook for Windows server configuration

1. Select Automation Engineer persona
2. Choose "Create Ansible Playbook" task
3. Follow the guided workflow:
   - Requirements analysis
   - Six-phase lifecycle design
   - Playbook structure development
   - Testing and validation
   - AAP integration
   - Documentation and handoff
```

## Best Practices

### Augment Code Integration

1. **Use Context Effectively:**
   - Let Augment Code index your entire project
   - Reference existing infrastructure code and documentation
   - Leverage cross-file navigation and understanding

2. **Leverage AI Suggestions:**
   - Accept AI-powered code completions for Ansible tasks
   - Use intelligent refactoring suggestions
   - Take advantage of context-aware variable suggestions

3. **Maintain Documentation:**
   - Keep infrastructure documentation current
   - Use consistent naming conventions
   - Leverage template-driven documentation

### Framework Usage

1. **Follow the Six-Phase Lifecycle:**
   - Configuration → Loading → Execution → Error Handling → Reporting → Cleanup
   - Ensure idempotent operations
   - Implement comprehensive logging

2. **Use Quality Gates:**
   - Validate against checklists at each phase
   - Ensure stakeholder approval
   - Maintain audit trails

3. **Collaborate Effectively:**
   - Use appropriate personas for specific tasks
   - Switch personas when expertise requirements change
   - Document decisions and rationale

### Project Organization

1. **Structure Your Project:**
   ```
   project-root/
   ├── bmad-agent/           # IaCOps framework
   ├── docs/
   │   ├── infrastructure/   # Architecture docs
   │   ├── security/        # Security policies
   │   └── operations/      # Operational procedures
   ├── ansible/
   │   ├── playbooks/       # Ansible playbooks
   │   ├── roles/           # Ansible roles
   │   └── inventory/       # Inventory files
   ├── pipelines/           # CI/CD pipelines
   └── monitoring/          # Monitoring configs
   ```

2. **Use Consistent Naming:**
   - Follow organizational naming conventions
   - Use descriptive file and directory names
   - Maintain consistent documentation structure

## Troubleshooting

### Common Issues

1. **Orchestrator Not Loading:**
   - Verify bmad-agent folder is in project root
   - Check file permissions
   - Ensure Augment Code extension is active

2. **Persona Not Available:**
   - Check ide-bmad-orchestrator-cfg.md configuration
   - Verify persona files exist in personas/ directory
   - Restart Augment Code if necessary

3. **Task Execution Errors:**
   - Verify task files exist in tasks/ directory
   - Check task file syntax and formatting
   - Ensure required templates and checklists are available

### Getting Help

1. **Framework Documentation:**
   - Review bmad-agent/data/bmad-kb.md for comprehensive guidance
   - Check specific persona documentation for detailed capabilities
   - Reference task files for step-by-step procedures

2. **Augment Code Support:**
   - Use Augment Code's built-in help and documentation
   - Leverage the context engine for code understanding
   - Utilize AI-powered suggestions and completions

3. **Community Resources:**
   - Consult organizational DevOps documentation
   - Reference Ansible and infrastructure best practices
   - Engage with infrastructure teams for guidance

## Practical Tutorial: Step-by-Step Infrastructure Project

### Tutorial: Designing OpenShift Infrastructure

Let's walk through a complete example of using the IaCOps Framework to design Red Hat OpenShift infrastructure.

#### Step 1: Initialize the Project

1. **Open VS Code with Augment Code:**
   ```bash
   code /path/to/your/openshift-project
   ```

2. **Start Augment Code Chat:**
   - Press `Ctrl+Shift+P`
   - Type "Augment: Open Chat"
   - Click on the Augment Code chat panel

3. **Load the IaCOps Orchestrator:**
   ```
   Load the IaCOps orchestrator from bmad-agent/ide-bmad-orchestrator.md and use the configuration from bmad-agent/ide-bmad-orchestrator-cfg.md. I need to design infrastructure for a Red Hat OpenShift deployment.
   ```

#### Step 2: Select Infrastructure Architect

**Chat Interaction:**
```
Orchestrator: IaCOps Orchestrator ready. Config loaded.

Available infrastructure personas:
- Infrastructure Architect (Alex) - Strategic infrastructure design and capacity planning
- Platform Engineer (Jordan) - OpenShift and container orchestration expertise
- Systems Engineer (Sam) - Server and virtualization configuration
- Automation Engineer (Casey) - Ansible automation and AAP workflows
- DevOps Engineer (Taylor) - End-to-end pipelines and monitoring
- Security Engineer (Morgan) - Security compliance and hardening
- Operations Engineer (Riley) - Incident response and troubleshooting

Which persona shall I become, and what task should it perform?

User: Infrastructure Architect - Design Infrastructure Architecture

Orchestrator: Activating Infrastructure Architect (Alex)...

As Infrastructure Architect, executing task: Design Infrastructure Architecture.

I'll help you design a comprehensive infrastructure architecture for your Red Hat OpenShift deployment. Let me start by analyzing your requirements...
```

#### Step 3: Requirements Gathering

**Alex (Infrastructure Architect) will guide you through:**

1. **Business Requirements Analysis:**
   ```
   Alex: Let's start with your business requirements. Please provide:

   1. What is the primary purpose of this OpenShift deployment?
   2. How many applications do you expect to host?
   3. What are your performance requirements?
   4. What are your availability requirements (SLA)?
   5. Do you have any compliance requirements?
   ```

2. **Current Infrastructure Assessment:**
   ```
   Alex: Now let's assess your current infrastructure:

   1. What virtualization platform are you using? (VMware ESXi/Nutanix HCI)
   2. What are your current server specifications?
   3. What network infrastructure do you have?
   4. What storage systems are available?
   5. What monitoring tools are currently in use?
   ```

#### Step 4: Architecture Design Process

**Alex will create comprehensive documentation:**

1. **High-Level Architecture:**
   - OpenShift cluster topology
   - Network segmentation strategy
   - Storage architecture
   - Integration points

2. **Detailed Component Design:**
   - Master node specifications
   - Worker node sizing
   - Infrastructure node requirements
   - Load balancer configuration

3. **Capacity Planning:**
   - Resource calculations
   - Growth projections
   - Performance optimization

#### Step 5: Switch to Platform Engineer

**When ready for OpenShift-specific details:**
```
User: I need detailed OpenShift configuration now

Alex: For detailed OpenShift configuration, I recommend switching to the Platform Engineer persona. They specialize in container orchestration and OpenShift specifics.

User: override safety protocol - switch to Platform Engineer

Orchestrator: Override confirmed. Terminating Infrastructure Architect. Re-initializing for Platform Engineer...

Activating Platform Engineer (Jordan)...

Jordan: I'm now your Platform Engineer specializing in Red Hat OpenShift. I can help you with:
- Design Deployment Pipeline
- Create OpenShift Configuration
- Design Container Strategy

What specific OpenShift configuration do you need?
```

#### Step 6: OpenShift Configuration

**Jordan (Platform Engineer) will help with:**

1. **Cluster Configuration:**
   - Node roles and specifications
   - Network policies and SDN configuration
   - Storage classes and persistent volumes
   - Security contexts and RBAC

2. **Deployment Pipeline:**
   - CI/CD integration with existing tools
   - Container registry configuration
   - Automated deployment strategies

#### Step 7: Automation Implementation

**Switch to Automation Engineer for Ansible playbooks:**
```
User: override safety protocol - switch to Automation Engineer

Casey: I'm now your Automation Engineer. I can help you create Ansible playbooks for your OpenShift infrastructure deployment following our six-phase lifecycle framework.

Available tasks:
- Create Ansible Playbook
- Design Automation Workflow
- Create AAP Job Template

User: Create Ansible Playbook

Casey: I'll help you create a comprehensive Ansible playbook for OpenShift deployment. Let's start with requirements analysis...
```

### Advanced Usage Tips

#### 1. Leveraging Augment Code Context

**Use the codebase-retrieval tool effectively:**
```
User: @codebase-retrieval Show me existing Ansible roles for RHEL configuration

[Augment Code will search your codebase and provide relevant context]

Casey: Based on your existing RHEL roles, I can see you already have user management and security hardening. Let me build on these for the OpenShift deployment...
```

#### 2. Cross-Persona Collaboration

**Reference previous work:**
```
User: Use the infrastructure architecture that Alex created earlier

Jordan: I can see the infrastructure architecture document in docs/infrastructure/. Based on Alex's design with 3 master nodes and 6 worker nodes, I'll configure the OpenShift cluster accordingly...
```

#### 3. Template Usage

**Leverage framework templates:**
```
Casey: I'm using the ansible-playbook-tmpl.md template to ensure our playbook follows organizational standards. The six-phase lifecycle will include:

1. Configuration - Variable loading and validation
2. Loading - Inventory discovery and credential retrieval
3. Execution - OpenShift installation and configuration
4. Error Handling - Rollback procedures and error logging
5. Reporting - Status updates and audit logging
6. Cleanup - Temporary resource cleanup
```

### Integration with Development Workflow

#### 1. Version Control Integration

**Commit infrastructure code:**
```bash
# The framework generates infrastructure code that integrates with your workflow
git add docs/infrastructure/openshift-architecture.md
git add ansible/playbooks/openshift-deployment.yml
git add pipelines/openshift-ci-cd.yml
git commit -m "Add OpenShift infrastructure design and automation

- Infrastructure architecture with 3 master, 6 worker nodes
- Ansible playbook with six-phase lifecycle
- CI/CD pipeline for automated deployment
- Security hardening and compliance validation"
```

#### 2. Continuous Integration

**The framework supports CI/CD integration:**
- Ansible playbooks include testing and validation
- Infrastructure documentation is version controlled
- Quality gates ensure compliance and standards
- Automated testing with Molecule framework

---

This comprehensive guide and tutorial demonstrate how the IaCOps Framework integrates seamlessly with Augment Code VS Code IDE to provide AI-driven infrastructure expertise. The combination of specialized personas, structured workflows, and intelligent code assistance creates a powerful platform for Infrastructure as Code development and operations.
