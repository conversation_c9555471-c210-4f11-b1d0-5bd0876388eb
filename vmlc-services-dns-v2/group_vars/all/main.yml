---
# =========================================================================
# Global Variables for DNS Management Automation v2
# =========================================================================
# This file contains global variables used across all environments for
# DNS management operations following OXAF infrastructure patterns.
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON><PERSON><PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

# =========================
# Global Configuration
# =========================

# Solution metadata
dns_solution:
  name: "DNS Management Automation v2"
  version: "2.0"
  framework: "Operational Excellence Automation Framework (OXAF)"
  author: "CES Operational Excellence Team"
  contributor: "Muhammad Syazani Bin Mohamed Khairi (7409)"

# Environment configuration
dns_environment: "{{ env | default('production') }}"
deployment_mode: "{{ mode | default('standard') }}"

# =========================
# Execution Configuration
# =========================

# Default operation settings
default_operation_settings:
  ttl: 3600
  timeout: 1800
  retry_count: 3
  validation_enabled: true
  rollback_enabled: true
  audit_enabled: true

# Testing and validation
testing_configuration:
  dry_run_mode: "{{ dry_run | default(false) }}"
  validation_only: "{{ validate_only | default(false) }}"
  skip_rollback: "{{ skip_rollback | default(false) }}"
  verbose_logging: "{{ verbose | default(false) }}"

# =========================
# Security Configuration
# =========================

# CyberArk configuration (collection-based approach)
cyberark_configuration:
  enabled: true
  # Collection handles all configuration automatically
  # No additional configuration required

# Security policies
security_policies:
  encryption_required: true
  audit_all_operations: true
  credential_rotation: true
  access_logging: true
  compliance_mode: "hipaa"

# =========================
# Logging Configuration
# =========================

# Global logging settings
logging_configuration:
  enabled: true
  level: "{{ log_level | default('INFO') }}"
  format: "structured"
  retention_days: 90
  max_file_size_mb: 100
  compression_enabled: true

# Log file naming convention
log_naming:
  pattern: "{{ tower_job_id | default('LOCAL') }}_{{ ansible_date_time.day }}{{ ansible_date_time.month }}{{ ansible_date_time.year }}_{{ var_sr_number | default('NOTICKET') }}_DNS_ANSIBLE.log"
  directory: "C:\\OE_AAP_LOGS"
  backup_directory: "C:\\OE_AAP_LOGS\\backup"

# Audit logging
audit_configuration:
  enabled: true
  include_sensitive_data: false
  compliance_format: true
  real_time_forwarding: true
  siem_integration: true

# =========================
# Performance Configuration
# =========================

# Timeout settings
timeout_configuration:
  connection_timeout: 30
  operation_timeout: 300
  total_execution_timeout: 1800
  script_execution_timeout: 900

# Concurrency settings
concurrency_configuration:
  max_parallel_operations: 5
  batch_size: 10
  queue_size: 50
  worker_threads: 4

# Performance monitoring
performance_monitoring:
  enabled: true
  metrics_collection: true
  threshold_monitoring: true
  alerting_enabled: true

# =========================
# Error Handling Configuration
# =========================

# Error handling policies
error_handling:
  automatic_retry: true
  max_retry_attempts: 3
  retry_delay_seconds: 30
  exponential_backoff: true
  rollback_on_failure: true

# Error classification
error_classification:
  critical_errors:
    - "authentication_failure"
    - "dns_server_unreachable"
    - "invalid_credentials"
    - "zone_not_found"
  recoverable_errors:
    - "temporary_network_issue"
    - "dns_record_conflict"
    - "timeout_error"
  warning_conditions:
    - "record_already_exists"
    - "record_not_found"
    - "ptr_record_missing"

# =========================
# Notification Configuration
# =========================

# Notification settings
notification_configuration:
  enabled: true
  channels:
    email: true
    teams: false
    slack: false
    sms: false

  # Email configuration
  email_settings:
    smtp_server: "{{ smtp_server | default('smtp.organization.com') }}"
    smtp_port: "{{ smtp_port | default(587) }}"
    use_tls: true
    sender: "{{ notification_sender | default('<EMAIL>') }}"

  # Notification triggers
  triggers:
    on_success: true
    on_failure: true
    on_warning: true
    on_rollback: true

# Default notification recipients
notification_recipients:
  default: "{{ var_notification_email | default('<EMAIL>') }}"
  escalation: "{{ var_escalation_email | default('<EMAIL>') }}"
  security: "{{ var_security_email | default('<EMAIL>') }}"
  dns_engineers: "{{ var_dns_engineers_email | default('<EMAIL>') }}"

# =========================
# Integration Configuration
# =========================

# ITSM Integration
itsm_integration:
  enabled: true
  system: "jira"
  update_tickets: true
  create_change_requests: false
  approval_required: false

  # JIRA Configuration
  jira_config:
    timeout_seconds: 30
    retry_attempts: 3
    retry_delay_seconds: 5
    validate_ssl: false

    # JIRA API endpoints
    api_version: "2"
    grid_api_version: "1.0"

    # Update configuration
    update_method: "grid_api"  # grid_api or rest_api
    update_field: "remark"

    # Error handling
    ignore_errors: true
    log_failures: true
    notify_on_failure: true

# Monitoring Integration
monitoring_integration:
  enabled: true
  platforms:
    - "splunk"
    - "prometheus"
  metrics_endpoint: "{{ metrics_endpoint | default('') }}"
  alerting_enabled: true

# =========================
# Compliance Configuration
# =========================

# Compliance requirements
compliance_configuration:
  frameworks:
    hipaa: true
    pci_dss: false
    sox: false
    iso27001: true

  # Audit requirements
  audit_requirements:
    change_tracking: true
    approval_workflows: false
    segregation_of_duties: true
    data_retention: true

# Data protection
data_protection:
  encryption_at_rest: true
  encryption_in_transit: true
  data_classification: "internal"
  retention_period_days: 2555  # 7 years

# =========================
# Quality Assurance Configuration
# =========================

# Quality gates
quality_gates:
  ansible_lint_required: true
  molecule_testing_required: true
  security_scanning_required: true
  documentation_required: true
  approval_required: false

# Testing configuration
testing_requirements:
  unit_tests: true
  integration_tests: true
  performance_tests: true
  security_tests: true
  idempotency_tests: true

# =========================
# Backup and Recovery Configuration
# =========================

# Backup settings
backup_configuration:
  enabled: true
  backup_before_change: true
  retention_days: 30
  compression: true
  encryption: true

# Recovery settings
recovery_configuration:
  automatic_rollback: true
  manual_rollback_available: true
  point_in_time_recovery: false
  disaster_recovery_enabled: false

# =========================
# Maintenance Configuration
# =========================

# Maintenance windows
maintenance_windows:
  production:
    start_time: "02:00"
    end_time: "04:00"
    timezone: "Asia/Singapore"
    days: ["Sunday"]
  staging:
    start_time: "01:00"
    end_time: "05:00"
    timezone: "Asia/Singapore"
    days: ["Saturday", "Sunday"]

# Health checks
health_checks:
  enabled: true
  frequency_minutes: 15
  timeout_seconds: 30
  alert_on_failure: true

# =========================
# Feature Flags
# =========================

# Feature toggles
feature_flags:
  enhanced_logging: true
  performance_monitoring: true
  automatic_rollback: true
  real_time_validation: true
  bulk_operations: false
  api_integration: false
  web_interface: false

# Experimental features
experimental_features:
  ai_powered_validation: false
  predictive_analytics: false
  auto_scaling: false
  multi_cloud_support: false
