# =========================================================================
# JIRA Service Request Update Handler - Audit Logging Role
# =========================================================================
# This handler updates JIRA Service Request tickets with DNS automation
# execution details and AAP job links for audit and tracking purposes
#
# Framework: Operational Excellence Automation Framework (OXAF)
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

---
# =========================
# JIRA Update Handler - Main Entry Point
# =========================

- name: "JIRA Integration - Initialize Update Process"
  debug:
    msg: |
      Starting JIRA Service Request update process
      SR Number: {{ var_sr_number | default('NOTICKET') }}
      Environment: {{ environment | default('production') }}
      JIRA Integration Enabled: {{ itsm_integration.enabled | default(false) }}
  when: 
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"
    - var_sr_number != ""

# =========================
# Pre-Update Validation
# =========================

- name: "JIRA Integration - Validate Service Request Number"
  assert:
    that:
      - var_sr_number is defined
      - var_sr_number != "NOTICKET"
      - var_sr_number != ""
      - var_sr_number is match("^SR-\\d+$")
    fail_msg: "Invalid service request number format. Expected: SR-XXXXXX"
    success_msg: "Service request number validation passed: {{ var_sr_number }}"
  when: 
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined

- name: "JIRA Integration - Validate Required Variables"
  assert:
    that:
      - tower_job_id is defined or ansible_job_id is defined
      - aap_url is defined or tower_host is defined
    fail_msg: "Missing required variables for JIRA update (job_id or aap_url)"
    success_msg: "Required variables validation passed"
  when: 
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"

# =========================
# Environment-Specific JIRA Configuration
# =========================

- name: "JIRA Integration - Set Environment-Specific Configuration"
  set_fact:
    jira_instance_config: "{{ jira_environment_mapping[environment | default('production')] }}"
    jira_update_url: "{{ jira_credentials.grid_base_url }}/{{ var_grid_id | default('') }}/issue/{{ var_sr_number }}/"
    jira_auth_header: "{{ jira_credentials.grid_token }}"
    current_job_id: "{{ tower_job_id | default(ansible_job_id | default('UNKNOWN')) }}"
    current_aap_url: "{{ aap_url | default(tower_host | default('https://aap.example.com')) }}"
  when: 
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"

# =========================
# JIRA Update Message Preparation
# =========================

- name: "JIRA Integration - Prepare Update Message"
  set_fact:
    jira_update_message: "{{ current_aap_url }}/#/jobs/playbook/{{ current_job_id }}/"
    jira_enhanced_message: |
      DNS Automation Execution Details:
      - Job ID: {{ current_job_id }}
      - Operation: {{ action | default('unknown') }}
      - Domain: {{ domain | default('N/A') }}
      - Hostname: {{ hostname | default('N/A') }}
      - Status: {{ dns_operation_status | default('completed') }}
      - Execution Time: {{ ansible_date_time.iso8601 }}
      - AAP Job: {{ current_aap_url }}/#/jobs/playbook/{{ current_job_id }}/
  when: 
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"

# =========================
# JIRA Grid API Update (Primary Method)
# =========================

- name: "JIRA Integration - Update Service Request via Grid API"
  uri:
    url: "{{ jira_update_url }}"
    headers:
      Authorization: "{{ jira_auth_header }}"
      Content-Type: "application/json"
    method: PUT
    status_code: [200, 201, 204]
    timeout: "{{ itsm_integration.jira_config.timeout_seconds | default(30) }}"
    validate_certs: "{{ itsm_integration.jira_config.validate_ssl | default(false) }}"
    body_format: json
    body: |
      {
        "rows": [
          {
            "rowId": "{{ var_row_id | default('1') }}",
            "columns": {
              "{{ jira_field_mapping.grid_api.remark | default('remark') }}": "{{ jira_update_message }}"
            }
          }
        ]
      }
  register: jira_update_result
  retries: "{{ itsm_integration.jira_config.retry_attempts | default(3) }}"
  delay: "{{ itsm_integration.jira_config.retry_delay_seconds | default(5) }}"
  when: 
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"
    - var_grid_id is defined
    - var_row_id is defined
    - itsm_integration.jira_config.update_method | default('grid_api') == 'grid_api'
  ignore_errors: "{{ itsm_integration.jira_config.ignore_errors | default(true) }}"

# =========================
# JIRA REST API Update (Fallback Method)
# =========================

- name: "JIRA Integration - Update Service Request via REST API (Fallback)"
  uri:
    url: "{{ jira_credentials.api_base_url }}/issue/{{ var_sr_number }}/comment"
    headers:
      Authorization: "Basic {{ (jira_credentials.username + ':' + jira_credentials.password) | b64encode }}"
      Content-Type: "application/json"
    method: POST
    status_code: [200, 201]
    timeout: "{{ itsm_integration.jira_config.timeout_seconds | default(30) }}"
    validate_certs: "{{ itsm_integration.jira_config.validate_ssl | default(false) }}"
    body_format: json
    body: |
      {
        "body": "{{ jira_enhanced_message }}"
      }
  register: jira_comment_result
  retries: "{{ itsm_integration.jira_config.retry_attempts | default(3) }}"
  delay: "{{ itsm_integration.jira_config.retry_delay_seconds | default(5) }}"
  when: 
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"
    - (jira_update_result is failed or itsm_integration.jira_config.update_method | default('grid_api') == 'rest_api')
  ignore_errors: "{{ itsm_integration.jira_config.ignore_errors | default(true) }}"

# =========================
# JIRA Update Result Processing
# =========================

- name: "JIRA Integration - Process Update Results"
  set_fact:
    jira_update_success: >-
      {{
        (jira_update_result is succeeded) or
        (jira_comment_result is succeeded)
      }}
    jira_update_method_used: >-
      {{
        'grid_api' if (jira_update_result is succeeded) else
        'rest_api' if (jira_comment_result is succeeded) else
        'failed'
      }}
  when: 
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"

# =========================
# JIRA Update Logging
# =========================

- name: "JIRA Integration - Log Successful Update"
  debug:
    msg: |
      JIRA Service Request Update - SUCCESS
      SR Number: {{ var_sr_number }}
      Method Used: {{ jira_update_method_used }}
      Job Link: {{ jira_update_message }}
      Response Status: {{ jira_update_result.status | default(jira_comment_result.status | default('N/A')) }}
  when: 
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"
    - jira_update_success | default(false) | bool

- name: "JIRA Integration - Log Failed Update"
  debug:
    msg: |
      JIRA Service Request Update - FAILED
      SR Number: {{ var_sr_number }}
      Grid API Error: {{ jira_update_result.msg | default('N/A') }}
      REST API Error: {{ jira_comment_result.msg | default('N/A') }}
      Will continue execution as ignore_errors is enabled
  when: 
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"
    - not (jira_update_success | default(false) | bool)

# =========================
# JIRA Update Metrics Collection
# =========================

- name: "JIRA Integration - Collect Update Metrics"
  set_fact:
    jira_metrics:
      update_attempted: true
      update_successful: "{{ jira_update_success | default(false) }}"
      method_used: "{{ jira_update_method_used | default('none') }}"
      response_time_ms: "{{ jira_update_result.elapsed | default(jira_comment_result.elapsed | default(0)) * 1000 }}"
      retry_count: "{{ jira_update_result.attempts | default(jira_comment_result.attempts | default(0)) }}"
      timestamp: "{{ ansible_date_time.iso8601 }}"
      sr_number: "{{ var_sr_number }}"
      job_id: "{{ current_job_id }}"
  when: 
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"
    - jira_metrics.track_response_time | default(false) | bool

# =========================
# JIRA Update Notification
# =========================

- name: "JIRA Integration - Send Failure Notification"
  debug:
    msg: |
      JIRA UPDATE FAILURE NOTIFICATION
      Service Request: {{ var_sr_number }}
      DNS Operation: {{ action | default('unknown') }} for {{ hostname | default('N/A') }}.{{ domain | default('N/A') }}
      Job ID: {{ current_job_id }}
      Error Details: Grid API and REST API both failed
      Manual update may be required
  when: 
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"
    - not (jira_update_success | default(false) | bool)
    - itsm_integration.jira_config.notify_on_failure | default(true) | bool

# =========================
# JIRA Integration Completion
# =========================

- name: "JIRA Integration - Update Process Complete"
  debug:
    msg: |
      JIRA Service Request update process completed
      SR Number: {{ var_sr_number }}
      Success: {{ jira_update_success | default(false) }}
      Method: {{ jira_update_method_used | default('none') }}
      Job Link: {{ jira_update_message | default('N/A') }}
  when: 
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"
