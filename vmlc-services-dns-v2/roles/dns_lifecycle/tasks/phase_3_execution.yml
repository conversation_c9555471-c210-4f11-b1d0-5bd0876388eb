---
# =========================================================================
# DNS Lifecycle - Phase 3: Execution
# =========================================================================
# This phase handles the core DNS operations execution with comprehensive
# error handling, progress monitoring, and validation following OXAF
# infrastructure automation patterns.
#
# Phase Objectives:
# - Core DNS operations (verify, add, remove, update)
# - Progress monitoring and status updates
# - Task result validation and verification
# - Intermediate checkpoint creation
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

- name: "Phase 3 Execution - Start"
  debug:
    msg: |
      Starting DNS Lifecycle Phase 3: Execution
      ========================================
      Phase: {{ phase_name }}
      Description: {{ phase_description }}
      Operation: {{ dns_execution_context.action }}
      Target: {{ dns_execution_context.hostname }}.{{ dns_execution_context.domain }}
      Primary Server: {{ dns_execution_context.primary_server }}

- name: "Phase 3.1 - Pre-execution Checkpoint"
  block:
    - name: "Create Execution Checkpoint"
      set_fact:
        execution_checkpoint:
          timestamp: "{{ ansible_date_time.iso8601 }}"
          phase: "execution"
          operation: "{{ dns_execution_context.action }}"
          target: "{{ dns_execution_context.hostname }}.{{ dns_execution_context.domain }}"
          server: "{{ dns_execution_context.primary_server }}"
          status: "starting"

    - name: "Log Execution Checkpoint"
      debug:
        msg: |
          Execution Checkpoint Created:
          ============================
          Timestamp: {{ execution_checkpoint.timestamp }}
          Operation: {{ execution_checkpoint.operation }}
          Target: {{ execution_checkpoint.target }}
          Server: {{ execution_checkpoint.server }}

    - name: "Update Audit Log with Checkpoint"
      ansible.windows.win_lineinfile:
        path: "{{ dns_log_config.directory }}\\{{ dns_log_config.file_name }}"
        line: |
          {{ ansible_date_time.iso8601 }} [AUDIT] EXECUTION_CHECKPOINT: {{ execution_checkpoint | to_json }}
      delegate_to: "{{ dns_execution_context.primary_server }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      when: dns_log_config.enabled | bool

- name: "Phase 3.2 - Deploy Enhanced DNS Script"
  block:
    - name: "Deploy Enhanced DNS Management Script"
      ansible.windows.win_copy:
        src: "{{ role_path }}/../dns_operations/files/set-dns-v2.ps1"
        dest: "C:\\ansscripts\\set-dns-v2.ps1"
        backup: true
      delegate_to: "{{ item }}"
      loop: "{{ dns_execution_context.servers }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: script_deployment_result

    - name: "Validate Script Deployment"
      ansible.windows.win_stat:
        path: "C:\\ansscripts\\set-dns-v2.ps1"
      delegate_to: "{{ dns_execution_context.primary_server }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: script_validation

    - name: "Verify Script Deployment Success"
      assert:
        that:
          - script_validation.stat.exists
          - script_validation.stat.size > 0
        fail_msg: "DNS script deployment failed or script is empty"
        success_msg: "DNS script deployed successfully"

    - name: "Log Script Deployment Status"
      debug:
        msg: |
          Script Deployment Status:
          ========================
          Status: SUCCESS
          Script Path: C:\ansscripts\set-dns-v2.ps1
          Script Size: {{ script_validation.stat.size }} bytes
          Deployed to: {{ dns_execution_context.servers | length }} servers

  rescue:
    - name: "Script Deployment Failed"
      fail:
        msg: "Phase 3 Execution failed during script deployment: {{ ansible_failed_result.msg }}"

- name: "Phase 3.3 - Execute DNS Operation"
  block:
    - name: "Set Operation Parameters"
      set_fact:
        dns_operation_params:
          action: "{{ dns_execution_context.action }}"
          domain: "{{ dns_execution_context.domain }}"
          hostname: "{{ dns_execution_context.hostname }}"
          ip_address: "{{ dns_execution_context.ip_address if dns_execution_context.action == 'add' else '' }}"
          ttl: "{{ dns_execution_context.ttl }}"
          log_file: "{{ dns_log_config.directory }}\\{{ dns_log_config.file_name }}"
          execution_id: "{{ execution_context.id }}"
          job_id: "{{ execution_context.job_id }}"
          dry_run: "{{ testing_mode | default(false) }}"
          enable_rollback: true
          verbose: true

    - name: "Log Operation Parameters"
      debug:
        msg: |
          DNS Operation Parameters:
          ========================
          Action: {{ dns_operation_params.action }}
          Domain: {{ dns_operation_params.domain }}
          Hostname: {{ dns_operation_params.hostname }}
          IP Address: {{ dns_operation_params.ip_address if dns_operation_params.ip_address else 'N/A' }}
          TTL: {{ dns_operation_params.ttl }}
          Dry Run: {{ dns_operation_params.dry_run }}
          Rollback Enabled: {{ dns_operation_params.enable_rollback }}

    - name: "Execute DNS Operation - {{ dns_operation_params.action | upper }}"
      ansible.windows.win_shell: |
        $ErrorActionPreference = "Stop"

        # Set execution parameters
        $params = @{
            Action = "{{ dns_operation_params.action }}"
            Domain = "{{ dns_operation_params.domain }}"
            HostName = "{{ dns_operation_params.hostname }}"
            TTL = {{ dns_operation_params.ttl }}
            LogFile = "{{ dns_operation_params.log_file }}"
            ExecutionId = "{{ dns_operation_params.execution_id }}"
            JobId = "{{ dns_operation_params.job_id }}"
            EnableRollback = ${{ dns_operation_params.enable_rollback | lower }}
            Verbose = ${{ dns_operation_params.verbose | lower }}
        }

        # Add IP address for add operations
        {% if dns_operation_params.action == 'add' and dns_operation_params.ip_address %}
        $params.IpAddress = "{{ dns_operation_params.ip_address }}"
        {% endif %}

        # Add dry run flag if enabled
        {% if dns_operation_params.dry_run | bool %}
        $params.DryRun = $true
        {% endif %}

        # Execute the DNS script
        try {
            Write-Output "Starting DNS operation: {{ dns_operation_params.action }}"
            Write-Output "Target: {{ dns_operation_params.hostname }}.{{ dns_operation_params.domain }}"
            Write-Output "Execution ID: {{ dns_operation_params.execution_id }}"
            Write-Output "Job ID: {{ dns_operation_params.job_id }}"
            Write-Output "Timestamp: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
            Write-Output "----------------------------------------"

            & "C:\ansscripts\set-dns-v2.ps1" @params

            $exitCode = $LASTEXITCODE
            Write-Output "----------------------------------------"
            Write-Output "DNS operation completed with exit code: $exitCode"

            if ($exitCode -ne 0) {
                throw "DNS operation failed with exit code: $exitCode"
            }

        } catch {
            Write-Error "DNS operation failed: $($_.Exception.Message)"
            throw
        }
      args:
        chdir: "C:\\ansscripts\\"
      delegate_to: "{{ dns_execution_context.primary_server }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
        ansible_winrm_operation_timeout_sec: "{{ dns_performance_config.operation_timeout }}"
        ansible_winrm_read_timeout_sec: "{{ dns_performance_config.total_timeout }}"
      register: dns_operation_result
      timeout: "{{ dns_performance_config.total_timeout }}"

    - name: "Log DNS Operation Result"
      debug:
        msg: |
          DNS Operation Result:
          ====================
          Status: {{ 'SUCCESS' if dns_operation_result.rc == 0 else 'FAILED' }}
          Exit Code: {{ dns_operation_result.rc }}
          Duration: {{ dns_operation_result.delta if dns_operation_result.delta is defined else 'N/A' }}
          Output Lines: {{ dns_operation_result.stdout_lines | length if dns_operation_result.stdout_lines is defined else 0 }}

  rescue:
    - name: "DNS Operation Failed"
      set_fact:
        dns_operation_failed: true
        dns_operation_error: "{{ ansible_failed_result.msg | default('Unknown error during DNS operation') }}"

    - name: "Log DNS Operation Failure"
      debug:
        msg: |
          DNS Operation Failed:
          ====================
          Error: {{ dns_operation_error }}
          Operation: {{ dns_operation_params.action }}
          Target: {{ dns_operation_params.hostname }}.{{ dns_operation_params.domain }}
          Server: {{ dns_execution_context.primary_server }}

    - name: "Fail Phase 3 Execution"
      fail:
        msg: "Phase 3 Execution failed during DNS operation: {{ dns_operation_error }}"

- name: "Phase 3.4 - Post-execution Validation"
  block:
    - name: "Validate Operation Results"
      ansible.windows.win_shell: |
        # Validate the DNS operation results
        $params = @{
            Action = "verify"
            Domain = "{{ dns_execution_context.domain }}"
            HostName = "{{ dns_execution_context.hostname }}"
            LogFile = "{{ dns_log_config.directory }}\\{{ dns_log_config.file_name }}"
            ExecutionId = "{{ execution_context.id }}"
            JobId = "{{ execution_context.job_id }}"
            Verbose = $true
        }

        Write-Output "Validating DNS operation results..."
        & "C:\ansscripts\set-dns-v2.ps1" @params

        $exitCode = $LASTEXITCODE
        if ($exitCode -ne 0) {
            throw "Post-execution validation failed with exit code: $exitCode"
        }
      delegate_to: "{{ dns_execution_context.primary_server }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: validation_result
      when:
        - dns_operation_result is defined
        - dns_operation_result.rc == 0
        - not (testing_mode | default(false) | bool)

    - name: "Log Validation Results"
      debug:
        msg: |
          Post-execution Validation:
          =========================
          Status: {{ 'SUCCESS' if validation_result.rc == 0 else 'FAILED' }}
          Validation Output: {{ validation_result.stdout_lines | join(' | ') if validation_result.stdout_lines is defined else 'N/A' }}
      when: validation_result is defined

  rescue:
    - name: "Post-execution Validation Failed"
      debug:
        msg: "Warning: Post-execution validation failed but operation may have succeeded: {{ ansible_failed_result.msg }}"

- name: "Phase 3.5 - Update Execution Status"
  block:
    - name: "Set Final Execution Status"
      set_fact:
        final_execution_status:
          phase: "execution"
          operation: "{{ dns_execution_context.action }}"
          target: "{{ dns_execution_context.hostname }}.{{ dns_execution_context.domain }}"
          server: "{{ dns_execution_context.primary_server }}"
          status: "{{ 'success' if dns_operation_result.rc == 0 else 'failed' }}"
          exit_code: "{{ dns_operation_result.rc if dns_operation_result.rc is defined else -1 }}"
          duration: "{{ dns_operation_result.delta if dns_operation_result.delta is defined else 'unknown' }}"
          timestamp: "{{ ansible_date_time.iso8601 }}"
          validation_status: "{{ 'success' if validation_result.rc == 0 else 'skipped' if validation_result is not defined else 'failed' }}"

    - name: "Update Audit Log with Final Status"
      ansible.windows.win_lineinfile:
        path: "{{ dns_log_config.directory }}\\{{ dns_log_config.file_name }}"
        line: |
          {{ ansible_date_time.iso8601 }} [AUDIT] EXECUTION_COMPLETE: {{ final_execution_status | to_json }}
      delegate_to: "{{ dns_execution_context.primary_server }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      when: dns_log_config.enabled | bool

    - name: "Set Global Execution Results"
      set_fact:
        dns_execution_results:
          operation_status: "{{ final_execution_status.status }}"
          operation_details: "{{ final_execution_status }}"
          operation_output: "{{ dns_operation_result.stdout_lines if dns_operation_result.stdout_lines is defined else [] }}"
          validation_output: "{{ validation_result.stdout_lines if validation_result.stdout_lines is defined else [] }}"

- name: "Phase 3 Execution - Complete"
  debug:
    msg: |
      DNS Lifecycle Phase 3: Execution - COMPLETED
      ===========================================
      Status: {{ final_execution_status.status | upper }}
      Operation: {{ final_execution_status.operation }}
      Target: {{ final_execution_status.target }}
      Server: {{ final_execution_status.server }}
      Exit Code: {{ final_execution_status.exit_code }}
      Duration: {{ final_execution_status.duration }}
      Validation: {{ final_execution_status.validation_status }}
      Next Phase: {{ 'Error Handling' if final_execution_status.status == 'failed' else 'Reporting' }}
