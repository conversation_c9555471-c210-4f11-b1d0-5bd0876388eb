{"vmlc-services-dns-v2/molecule/default/molecule.yml": ".yml", "vmlc-services-dns-v2/roles/audit_logging/meta/main.yml": ".yml", "vmlc-services-dns-v2/roles/audit_logging/handlers/update_service_request.yml": ".yml", "vmlc-services-dns-v2/roles/dns_lifecycle/defaults/main.yml": ".yml", "vmlc-services-dns-v2/group_vars/development/main.yml": ".yml", "vmlc-services-dns-v2/group_vars/all/main.yml": ".yml", "vmlc-services-dns-v2/group_vars/all/jira_config.yml": ".yml", "vmlc-services-dns-v2/roles/dns_lifecycle/meta/main.yml": ".yml", "vmlc-services-dns-v2/roles/dns_lifecycle/tasks/phase_1_configuration.yml": ".yml", "vmlc-services-dns-v2/roles/dns_lifecycle/tasks/phase_2_loading.yml": ".yml", "vmlc-services-dns-v2/roles/dns_lifecycle/tasks/phase_3_execution.yml": ".yml", "vmlc-services-dns-v2/roles/dns_lifecycle/tasks/main.yml": ".yml", "vmlc-services-dns-v2/roles/dns_lifecycle/tasks/phase_4_error_handling.yml": ".yml", "vmlc-services-dns-v2/roles/dns_lifecycle/tasks/phase_5_reporting.yml": ".yml", "vmlc-services-dns-v2/roles/dns_lifecycle/tasks/phase_6_cleanup.yml": ".yml", "vmlc-services-dns-v2/roles/dns_lifecycle/tasks/phase_error_handler.yml": ".yml", "vmlc-services-dns-v2/roles/dns_lifecycle/handlers/main.yml": ".yml", "vmlc-services-dns-v2/roles/dns_lifecycle/handlers/emergency_rollback.yml": ".yml", "vmlc-services-dns-v2/roles/dns_lifecycle/handlers/update_service_request.yml": ".yml", "vmlc-services-dns-v2/roles/dns_operations/files/set-dns-v2.ps1": ".ps1", "vmlc-services-dns-v2/README.md": ".md", "vmlc-services-dns-v2/main.yml": ".yml", "vmlc-services-dns-v2/ansible.cfg": ".cfg", "vmlc-services-dns-v2/roles/audit_logging/tasks/initialize_audit_log.yml": ".yml", "vmlc-services-dns-v2/roles/audit_logging/tasks/validate_jira_integration.yml": ".yml", "vmlc-services-dns-v2/group_vars/production/main.yml": ".yml", "vmlc-services-dns-v2/group_vars/staging/main.yml": ".yml", "vmlc-services-dns-v2/docs/USER_GUIDE.md": ".md", "vmlc-services-dns-v2/docs/INSTALLATION.md": ".md", "vmlc-services-dns-v2/docs/ZONE_CREATION_WORKFLOW.md": ".md", "vmlc-services-dns-v2/docs/UAT_POWERSHELL_TESTING.md": ".md", "vmlc-services-dns-v2/docs/UAT_QUICK_TEST_SCRIPT.ps1": ".ps1", "vmlc-services-dns-v2/docs/UAT_USAGE_EXAMPLES.md": ".md", "vmlc-services-dns-v2/docs/REVERSE_ZONE_DETECTION_TEST.ps1": ".ps1", "vmlc-services-dns-v2/docs/INTELLIGENT_REVERSE_ZONE_DETECTION.md": ".md", "vmlc-services-dns-v2/docs/ZONE_SAFETY_VALIDATION_TEST.ps1": ".ps1", "vmlc-services-dns-v2/docs/ZONE_SAFETY_MECHANISMS.md": ".md", "vmlc-services-dns-v2/docs/AAP_JSON_REFERENCE.md": ".md", "vmlc-services-dns-v2/docs/AAP_WORKFLOW_GUIDE.md": ".md", "vmlc-services-dns-v2/docs/AAP_YAML_REFERENCE.md": ".md", "vmlc-services-dns-v2/docs/YAML_BEST_PRACTICES.md": ".md", "vmlc-services-dns-v2/docs/JIRA_INTEGRATION_GUIDE.md": ".md", "vmlc-services-dns-v2/docs/CYBERARK_INTEGRATION_GUIDE.md": ".md", "vmlc-services-dns-v2/docs/CYBERARK_SETUP_GUIDE.md": ".md", "vmlc-services-dns-v2/docs/JIRA_TICKET_VALIDATION_TEST.ps1": ".ps1", "vmlc-services-dns-v2/docs/test_jira_validation.sh": ".sh", "vmlc-services-dns-v2/docs/JIRA_TICKET_VALIDATION_UPDATE_SUMMARY.md": ".md", "vmlc-services-dns-v2/docs/API_REFERENCE.md": ".md", "vmlc-services-dns-v2/docs/CONFIGURATION.md": ".md", "vmlc-services-dns-v2/docs/TROUBLESHOOTING.md": ".md", "vmlc-services-dns-v2/docs/AAP_MODULE_ERROR_RESOLUTION_SUMMARY.md": ".md", "vmlc-services-dns-v2/docs/AAP_RESERVED_VARIABLE_REMEDIATION_SUMMARY.md": ".md", "vmlc-services-dns-v2/docs/STANDARDIZED_VARIABLE_NAMING_CONVENTION.md": ".md", "vmlc-services-dns-v2/docs/STANDARDIZED_VARIABLE_IMPLEMENTATION_SUMMARY.md": ".md", "vmlc-services-dns-v2/inventory/production/hosts.yml": ".yml", "vmlc-services-dns-v2/collections/requirements.yml": ".yml"}