# JIRA Integration Guide - DNS Management Automation v2

## Comprehensive JIRA Service Request Integration

**Framework:** Operational Excellence Automation Framework (OXAF)
**Version:** 2.0
**Purpose:** Complete guide for JIRA integration setup and operation

---

## 🎯 **Overview**

The DNS Management Automation v2 solution includes comprehensive JIRA integration that automatically updates Service Request tickets with execution details, AAP job links, and operation status. This integration provides full audit traceability and seamless workflow integration.

### **Key Features:**
- ✅ **Automatic ticket updates** with AAP job links
- ✅ **Multi-environment support** (Production, Staging, Development)
- ✅ **Dual API support** (Grid API and REST API)
- ✅ **Enhanced error handling** and retry logic
- ✅ **Comprehensive validation** and testing
- ✅ **Detailed audit logging** and metrics collection

---

## 🔧 **JIRA Integration Architecture**

### **Integration Components:**

#### **1. Handler Files:**
- `roles/audit_logging/handlers/update_service_request.yml` - Main JIRA update handler
- `roles/dns_lifecycle/handlers/update_service_request.yml` - DNS-specific JIRA handler

#### **2. Configuration Files:**
- `group_vars/all/jira_config.yml` - JIRA configuration templates
- `group_vars/production/main.yml` - Production JIRA settings
- `group_vars/staging/main.yml` - Staging JIRA settings
- `group_vars/development/main.yml` - Development JIRA settings
- `group_vars/all/vault.yml` - Encrypted credentials

#### **3. Validation Module:**
- `roles/audit_logging/tasks/validate_jira_integration.yml` - JIRA connectivity testing

---

## 🌍 **Environment Configuration**

### **Production Environment:**
```yaml
# Production JIRA Configuration
itsm_integration:
  enabled: true
  system: "jira"
  var_environment: "production"

  jira_config:
    instance: "production"
    base_url: "https://itsm.hcloud.healthgrp.com.sg"
    project_key: "SR"
    timeout_seconds: 45
    retry_attempts: 5
    validate_ssl: true
    ignore_errors: false

# Production Credentials
jira_credentials:
  username: "{{ vault_jira_prod_username }}"
  password: "{{ vault_jira_prod_password }}"
  grid_token: "{{ vault_jira_prod_grid_token }}"
  api_base_url: "https://itsm.hcloud.healthgrp.com.sg/rest/api/2"
  grid_base_url: "https://itsm.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid"
```

### **Staging/Development Environment:**
```yaml
# Staging/Development JIRA Configuration (uses UAT instance)
itsm_integration:
  enabled: true
  system: "jira"
  var_environment: "staging"  # or "development"

  jira_config:
    instance: "uat"
    base_url: "https://jsd-uat.hcloud.healthgrp.com.sg"
    project_key: "SR"
    timeout_seconds: 30
    retry_attempts: 3
    validate_ssl: false
    ignore_errors: true

# UAT Credentials
jira_credentials:
  username: "{{ vault_jira_uat_username }}"
  password: "{{ vault_jira_uat_password }}"
  grid_token: "{{ vault_jira_uat_grid_token }}"
  api_base_url: "https://jsd-uat.hcloud.healthgrp.com.sg/rest/api/2"
  grid_base_url: "https://jsd-uat.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid"
```

---

## 🔐 **Credential Management**

### **Ansible Vault Encryption:**

#### **Encrypt New Credentials:**
```bash
# Encrypt JIRA username
ansible-vault encrypt_string 'svchispans01' --name 'vault_jira_prod_username'

# Encrypt JIRA password
ansible-vault encrypt_string 'actual_password' --name 'vault_jira_prod_password'

# Encrypt Grid API token
ansible-vault encrypt_string 'actual_grid_token' --name 'vault_jira_prod_grid_token'
```

#### **Required Vault Variables:**
```yaml
# Production
vault_jira_prod_username: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  [encrypted_content]

vault_jira_prod_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  [encrypted_content]

vault_jira_prod_grid_token: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  [encrypted_content]

# UAT (for staging/development)
vault_jira_uat_username: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  [encrypted_content]

vault_jira_uat_password: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  [encrypted_content]

vault_jira_uat_grid_token: !vault |
  $ANSIBLE_VAULT;1.1;AES256
  [encrypted_content]
```

---

## 🔄 **JIRA Update Methods**

### **Method 1: Grid API (Primary)**
The Grid API method updates specific fields in JIRA tickets using the Idalko Grid plugin:

```yaml
# Grid API Update
uri:
  url: "{{ jira_credentials.grid_base_url }}/{{ var_grid_id }}/issue/{{ var_sr_number }}/"
  headers:
    Authorization: "{{ jira_credentials.grid_token }}"
  method: PUT
  body_format: json
  body: |
    {
      "rows": [
        {
          "rowId": "{{ var_row_id }}",
          "columns": {
            "remark": "{{ aap_url }}/#/jobs/playbook/{{ tower_job_id }}/"
          }
        }
      ]
    }
```

### **Method 2: REST API (Fallback)**
The REST API method adds comments to JIRA tickets:

```yaml
# REST API Comment
uri:
  url: "{{ jira_credentials.api_base_url }}/issue/{{ var_sr_number }}/comment"
  headers:
    Authorization: "Basic {{ (username + ':' + password) | b64encode }}"
  method: POST
  body_format: json
  body: |
    {
      "body": "DNS Automation Execution Details:\n- Job ID: {{ tower_job_id }}\n- Operation: {{ action }}\n- Status: {{ status }}\n- AAP Job: {{ aap_url }}/#/jobs/playbook/{{ tower_job_id }}/"
    }
```

---

## 📋 **Required Variables**

### **Mandatory Variables:**
- `var_sr_number` - JIRA ticket number (supports multiple formats)
  - **Service Requests (SR):** `SR-XXXXXX` - Standard DNS operations
  - **Service Change Requests (SCR):** `SCR-XXXXXX` - Infrastructure changes
  - **Incidents (INC):** `INC-XXXXXX` - Emergency DNS fixes
- `tower_job_id` - AAP job ID for linking
- `aap_url` - AAP instance URL

### **Grid API Variables (if using Grid API):**
- `var_grid_id` - Grid ID for the specific JIRA project
- `var_row_id` - Row ID for the specific ticket entry

### **DNS Operation Variables:**
- `action` - DNS operation type (verify, add, remove, update, sync)
- `domain` - Target domain name
- `hostname` - Target hostname
- `ipaddress` - IP address (for add/update operations)

---

## 🎫 **JIRA Ticket Validation**

### **Supported Ticket Types:**
The DNS Management Automation v2 solution supports multiple JIRA ticket types to accommodate different operational scenarios:

#### **Service Requests (SR) - Standard Operations**
- **Format:** `SR-XXXXXX` (e.g., `SR-123456`)
- **Use Case:** Standard DNS record operations (add, remove, update, verify)
- **Approval:** Standard change approval process
- **Examples:** New server DNS entries, routine DNS updates

#### **Service Change Requests (SCR) - Infrastructure Changes**
- **Format:** `SCR-XXXXXX` (e.g., `SCR-789012`)
- **Use Case:** Infrastructure-related DNS changes
- **Approval:** Change advisory board approval
- **Examples:** Data center migrations, network infrastructure changes

#### **Incidents (INC) - Emergency Operations**
- **Format:** `INC-XXXXXX` (e.g., `INC-345678`)
- **Use Case:** Emergency DNS fixes and incident response
- **Approval:** Emergency change process
- **Examples:** Security incident response, service outage resolution

### **Validation Rules:**
- **Pattern:** `^(SR|SCR|INC)-\d+$`
- **Case Sensitive:** Yes (must be uppercase)
- **Required:** Yes (cannot be empty or "NOTICKET")
- **Automatic Validation:** Performed before DNS operations

### **Validation Examples:**
```yaml
# Valid ticket numbers
✅ SR-123456    # Service Request
✅ SCR-789012   # Service Change Request
✅ INC-345678   # Incident
✅ SR-1         # Minimum format
✅ SCR-999999   # Maximum typical format

# Invalid ticket numbers
❌ INVALID-123456  # Unsupported type
❌ REQ-123456      # Wrong format
❌ sr-123456       # Lowercase
❌ SR123456        # Missing hyphen
❌ SR-            # Missing number
❌ SR-ABC123       # Non-numeric
❌ ""              # Empty string
❌ NOTICKET        # Placeholder value
```

---

## 🧪 **JIRA Integration Testing**

### **Validation Process:**
The JIRA integration includes comprehensive validation that runs during the loading phase:

1. **Configuration Validation** - Verifies all required variables are present
2. **Connectivity Testing** - Tests connection to JIRA REST and Grid APIs
3. **Authentication Validation** - Verifies credentials are working
4. **Permission Testing** - Checks access to required JIRA resources

### **Test Execution:**
```yaml
# Enable JIRA validation
jira_testing:
  validate_connectivity: true
  validate_authentication: true
  validate_permissions: true
  test_ticket_prefix: "TEST-"
  test_grid_id: "999999"
  test_row_id: "999999"
```

### **Manual Testing:**
```bash
# Test JIRA connectivity with Service Request
ansible-playbook main.yml -e "action=verify domain=test.com hostname=test var_sr_number=SR-123456" --tags="validation,jira"

# Test with Service Change Request
ansible-playbook main.yml -e "action=verify domain=test.com hostname=test var_sr_number=SCR-789012" --tags="validation,jira"

# Test with Incident ticket
ansible-playbook main.yml -e "action=verify domain=test.com hostname=test var_sr_number=INC-345678" --tags="validation,jira"

# Test with specific environment
ansible-playbook main.yml -e "environment=staging action=verify domain=test.com hostname=test var_sr_number=SCR-123456"
```

---

## 📊 **JIRA Update Examples**

### **Standard AAP Job Link Update:**
```
https://aap.example.com/#/jobs/playbook/12345/
```

### **Enhanced DNS Operation Update:**
```
DNS Automation - ADD Operation

Target: web01.healthgrp.com.sg
IP Address: *************
TTL: 3600
Environment: PRODUCTION
Status: COMPLETED

Execution Details:
- Job ID: 12345
- Operator: automation
- Timestamp: 2024-01-15T10:30:00Z
- AAP Job: https://aap.example.com/#/jobs/playbook/12345/
```

### **Status-Specific Comments:**
```
✅ DNS Operation Successful

Operation: ADD
Target: web01.healthgrp.com.sg
IP: *************
Status: SUCCESS

Execution completed successfully with no errors.
```

---

## 🔍 **Monitoring and Troubleshooting**

### **JIRA Integration Logs:**
```yaml
# Successful Update Log
JIRA Service Request Update - SUCCESS
SR Number: SR-123456
Method Used: grid_api
Job Link: https://aap.example.com/#/jobs/playbook/12345/
Response Status: 204

# Failed Update Log
JIRA Service Request Update - FAILED
SR Number: SR-123456
Grid API Error: Connection timeout
REST API Error: Authentication failed
Will continue execution as ignore_errors is enabled
```

### **Common Issues and Solutions:**

#### **Issue 1: Authentication Failure**
```
Error: 401 Unauthorized
Solution:
1. Verify JIRA credentials in vault
2. Check service account permissions
3. Validate Grid API token
```

#### **Issue 2: Grid API Not Found**
```
Error: 404 Not Found on Grid API
Solution:
1. Verify var_grid_id is correct
2. Check if Idalko Grid plugin is installed
3. Fall back to REST API method
```

#### **Issue 3: Invalid JIRA Ticket Number**
```
Error: Invalid JIRA ticket number format. Expected: SR-XXXXXX, SCR-XXXXXX, or INC-XXXXXX
Solution:
1. Ensure var_sr_number follows supported format:
   - Service Requests: SR-XXXXXX
   - Service Change Requests: SCR-XXXXXX
   - Incidents: INC-XXXXXX
2. Verify ticket exists in JIRA
3. Check project permissions
4. Ensure ticket type is uppercase
```

---

## 📈 **Metrics and Reporting**

### **JIRA Integration Metrics:**
```yaml
jira_metrics:
  update_attempted: true
  update_successful: true
  method_used: "grid_api"
  response_time_ms: 1250
  retry_count: 0
  timestamp: "2024-01-15T10:30:00Z"
  sr_number: "SR-123456"
  job_id: "12345"
```

### **Performance Monitoring:**
- **Response Time Tracking** - Monitor JIRA API response times
- **Success Rate Monitoring** - Track update success/failure rates
- **Error Pattern Analysis** - Identify common failure patterns
- **Usage Analytics** - Monitor JIRA integration usage patterns

---

## 🔧 **Configuration Examples**

### **AAP Extra Variables for JIRA Integration:**

#### **Service Request (SR) - JSON Format:**
```json
{
  "var_action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "web01",
  "ipaddress": "*************",
  "var_sr_number": "SR-123456",
  "var_grid_id": "12345",
  "var_row_id": "1",
  "testing_mode": false
}
```

#### **Service Change Request (SCR) - JSON Format:**
```json
{
  "var_action": "update",
  "domain": "healthgrp.com.sg",
  "hostname": "db01",
  "ipaddress": "************",
  "var_sr_number": "SCR-789012",
  "var_grid_id": "12345",
  "var_row_id": "2",
  "testing_mode": false
}
```

#### **Incident (INC) - JSON Format:**
```json
{
  "var_action": "remove",
  "domain": "healthgrp.com.sg",
  "hostname": "compromised-server",
  "var_sr_number": "INC-345678",
  "testing_mode": false
}
```

#### **YAML Format Examples:**
```yaml
# Service Request
var_action: add
domain: healthgrp.com.sg
hostname: web01
ipaddress: *************
var_sr_number: SR-123456
var_grid_id: "12345"
var_row_id: "1"
testing_mode: false

---
# Service Change Request
var_action: update
domain: healthgrp.com.sg
hostname: db01
ipaddress: ************
var_sr_number: SCR-789012
var_grid_id: "12345"
var_row_id: "2"
testing_mode: false

---
# Incident
var_action: remove
domain: healthgrp.com.sg
hostname: compromised-server
var_sr_number: INC-345678
testing_mode: false
```

---

## 🚨 **Error Handling**

### **Error Handling Strategy:**
1. **Primary Method Failure** - Automatically retry with exponential backoff
2. **Fallback Method** - Switch from Grid API to REST API if primary fails
3. **Graceful Degradation** - Continue DNS operations even if JIRA update fails
4. **Comprehensive Logging** - Log all attempts and failures for troubleshooting

### **Error Notification:**
```yaml
# JIRA Update Failure Notification
JIRA UPDATE FAILURE NOTIFICATION
Service Request: SR-123456
DNS Operation: add for web01.healthgrp.com.sg
Job ID: 12345
Error Details: Grid API and REST API both failed
Manual update may be required
```

---

## 📞 **Support and Maintenance**

### **Regular Maintenance Tasks:**
1. **Credential Rotation** - Update JIRA service account passwords
2. **Permission Validation** - Verify service account permissions
3. **Performance Monitoring** - Monitor JIRA integration performance
4. **Error Analysis** - Review and address recurring errors

### **Support Contacts:**
- **DNS Automation Team:** <EMAIL>
- **JIRA Administration:** <EMAIL>
- **Infrastructure Team:** <EMAIL>

---

## 🧪 **JIRA Integration Testing Script**

### **Comprehensive Test Script:**
```bash
#!/bin/bash
# =========================================================================
# JIRA Integration Testing Script for DNS Management Automation v2
# =========================================================================

echo "JIRA Integration Testing - DNS Management Automation v2"
echo "======================================================="

# Test 1: JIRA Connectivity Validation
echo "Test 1: JIRA Connectivity Validation"
ansible-playbook main.yml \
  -e "action=verify domain=test.healthgrp.com.sg hostname=jira-test var_sr_number=SR-999999" \
  --tags="validation,jira" \
  --check

# Test 2: Production JIRA Integration (Service Request)
echo "Test 2: Production JIRA Integration (Service Request)"
ansible-playbook main.yml \
  -e "environment=production action=verify domain=healthgrp.com.sg hostname=jira-prod-test var_sr_number=SR-123456 var_grid_id=12345 var_row_id=1" \
  --limit="production_dns_servers"

# Test 3: Staging JIRA Integration (Service Change Request)
echo "Test 3: Staging JIRA Integration (Service Change Request)"
ansible-playbook main.yml \
  -e "environment=staging action=verify domain=stghealthgrp.com.sg hostname=jira-stg-test var_sr_number=SCR-789012" \
  --limit="staging_dns_servers"

# Test 4: Development JIRA Integration (Incident)
echo "Test 4: Development JIRA Integration (Incident)"
ansible-playbook main.yml \
  -e "environment=development action=verify domain=devhealthgrp.com.sg hostname=jira-dev-test var_sr_number=INC-345678" \
  --limit="development_dns_servers"

# Test 5: JIRA Integration with DNS Operations (Multiple Ticket Types)
echo "Test 5: JIRA Integration with DNS Operations"
ansible-playbook main.yml \
  -e "action=add domain=test.healthgrp.com.sg hostname=jira-sr-test ipaddress=************** var_sr_number=SR-TEST-001 testing_mode=true"

ansible-playbook main.yml \
  -e "action=update domain=test.healthgrp.com.sg hostname=jira-scr-test ipaddress=************** var_sr_number=SCR-TEST-002 testing_mode=true"

ansible-playbook main.yml \
  -e "action=remove domain=test.healthgrp.com.sg hostname=jira-inc-test var_sr_number=INC-TEST-003 testing_mode=true"

echo "JIRA Integration Testing Complete"
echo "Check logs and JIRA tickets for validation results"
```

### **Individual Test Commands:**

#### **Test JIRA Connectivity (Different Ticket Types):**
```bash
# Test with Service Request
ansible-playbook main.yml \
  -e "action=verify domain=test.com hostname=test var_sr_number=SR-123456" \
  --tags="validation,jira" \
  --check

# Test with Service Change Request
ansible-playbook main.yml \
  -e "action=verify domain=test.com hostname=test var_sr_number=SCR-789012" \
  --tags="validation,jira" \
  --check

# Test with Incident
ansible-playbook main.yml \
  -e "action=verify domain=test.com hostname=test var_sr_number=INC-345678" \
  --tags="validation,jira" \
  --check
```

#### **Test Grid API Update:**
```bash
# Test Grid API with Service Request
ansible-playbook main.yml \
  -e "action=verify domain=test.com hostname=test var_sr_number=SR-123456 var_grid_id=12345 var_row_id=1" \
  --tags="jira"

# Test Grid API with Service Change Request
ansible-playbook main.yml \
  -e "action=verify domain=test.com hostname=test var_sr_number=SCR-789012 var_grid_id=12345 var_row_id=2" \
  --tags="jira"
```

#### **Test REST API Fallback:**
```bash
# Test REST API with different ticket types
ansible-playbook main.yml \
  -e "action=verify domain=test.com hostname=test var_sr_number=SR-123456 jira_force_rest_api=true" \
  --tags="jira"

ansible-playbook main.yml \
  -e "action=verify domain=test.com hostname=test var_sr_number=INC-345678 jira_force_rest_api=true" \
  --tags="jira"
```

#### **Test Ticket Validation:**
```bash
# Test valid ticket formats
ansible-playbook main.yml \
  -e "action=verify domain=test.com hostname=test var_sr_number=SR-123456" \
  --tags="validation" \
  --check

# Test invalid ticket format (should fail)
ansible-playbook main.yml \
  -e "action=verify domain=test.com hostname=test var_sr_number=INVALID-123456" \
  --tags="validation" \
  --check
```

---

*This JIRA integration guide is part of the DNS Management Automation v2 solution developed by the CES Operational Excellence Team following Operational Excellence Automation Framework (OXAF) patterns.*
