---
# =========================================================================
# Production Environment Variables for DNS Management Automation v2
# =========================================================================
# This file contains production-specific variables for DNS management
# operations following OXAF infrastructure patterns.
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

# =========================
# Environment Configuration
# =========================

var_environment: "production"
deployment_mode: "production"

# =========================
# Production Security Settings
# =========================

# Enhanced security for production
security_policies:
  encryption_required: true
  audit_all_operations: true
  credential_rotation: true
  access_logging: true
  compliance_mode: "hipaa"
  enhanced_monitoring: true

# CyberArk production configuration (collection-based)
cyberark_configuration:
  enabled: true
  # Collection handles all configuration automatically

# =========================
# Production Logging Configuration
# =========================

# Enhanced logging for production
logging_configuration:
  enabled: true
  level: "INFO"
  format: "structured"
  retention_days: 2555  # 7 years for compliance
  max_file_size_mb: 100
  compression_enabled: true
  real_time_forwarding: true

# Production log paths
log_naming:
  pattern: "{{ tower_job_id | default('PROD') }}_{{ ansible_date_time.day }}{{ ansible_date_time.month }}{{ ansible_date_time.year }}_{{ var_sr_number | default('NOTICKET') }}_DNS_ANSIBLE.log"
  directory: "C:\\OE_AAP_LOGS\\PRODUCTION"
  backup_directory: "C:\\OE_AAP_LOGS\\PRODUCTION\\backup"
  archive_directory: "C:\\OE_AAP_LOGS\\PRODUCTION\\archive"

# =========================
# Production Performance Configuration
# =========================

# Conservative timeouts for production stability
timeout_configuration:
  connection_timeout: 45
  operation_timeout: 600
  total_execution_timeout: 3600
  script_execution_timeout: 1800

# Limited concurrency for production stability
concurrency_configuration:
  max_parallel_operations: 3
  batch_size: 5
  queue_size: 25
  worker_threads: 2

# =========================
# Production Error Handling
# =========================

# Strict error handling for production
error_handling:
  automatic_retry: true
  max_retry_attempts: 2
  retry_delay_seconds: 60
  exponential_backoff: true
  rollback_on_failure: true
  escalation_on_critical: true

# =========================
# Production Notification Configuration
# =========================

# Production notification settings
notification_configuration:
  enabled: true
  channels:
    email: true
    teams: true
    slack: false
    sms: true  # For critical alerts

  # Production email configuration
  email_settings:
    smtp_server: "{{ smtp_server_prod }}"
    smtp_port: 587
    use_tls: true
    sender: "<EMAIL>"

  # Enhanced notification triggers for production
  triggers:
    on_success: true
    on_failure: true
    on_warning: true
    on_rollback: true
    on_critical_error: true

# Production notification recipients
notification_recipients:
  default: "{{ var_notification_email | default('<EMAIL>') }}"
  escalation: "{{ var_escalation_email | default('<EMAIL>') }}"
  security: "{{ var_security_email | default('<EMAIL>') }}"
  management: "{{ var_management_email | default('<EMAIL>') }}"

# =========================
# Production Integration Configuration
# =========================

# ITSM Integration for production
itsm_integration:
  enabled: true
  system: "jira"
  update_tickets: true
  create_change_requests: true
  approval_required: true
  environment: "production"

  # Production JIRA Configuration
  jira_config:
    instance: "production"
    base_url: "https://itsm.hcloud.healthgrp.com.sg"
    project_key: "SR"

    # Production-specific settings
    timeout_seconds: 45
    retry_attempts: 5
    validate_ssl: true

    # Enhanced error handling for production
    ignore_errors: false
    log_failures: true
    notify_on_failure: true
    escalate_on_failure: true

# Production JIRA Credentials (CyberArk collection-based)
jira_credentials:
  # CyberArk account names for credential retrieval
  username_account: "JIRA_PROD_USERNAME"
  password_account: "JIRA_PROD_PASSWORD"
  grid_token_account: "JIRA_PROD_GRID_TOKEN"

  # API endpoints
  api_base_url: "https://itsm.hcloud.healthgrp.com.sg/rest/api/2"
  grid_base_url: "https://itsm.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid"

# Production monitoring integration
monitoring_integration:
  enabled: true
  platforms:
    - "splunk"
    - "prometheus"
    - "datadog"
  metrics_endpoint: "{{ metrics_endpoint_prod }}"
  alerting_enabled: true
  dashboard_enabled: true

# =========================
# Production Compliance Configuration
# =========================

# Strict compliance for production
compliance_configuration:
  frameworks:
    hipaa: true
    pci_dss: false
    sox: true
    iso27001: true

  # Enhanced audit requirements for production
  audit_requirements:
    change_tracking: true
    approval_workflows: true
    segregation_of_duties: true
    data_retention: true
    real_time_monitoring: true

# Production data protection
data_protection:
  encryption_at_rest: true
  encryption_in_transit: true
  data_classification: "confidential"
  retention_period_days: 2555  # 7 years
  backup_encryption: true

# =========================
# Production Quality Gates
# =========================

# Strict quality gates for production
quality_gates:
  ansible_lint_required: true
  molecule_testing_required: true
  security_scanning_required: true
  documentation_required: true
  approval_required: true
  change_management_required: true

# =========================
# Production Backup and Recovery
# =========================

# Enhanced backup for production
backup_configuration:
  enabled: true
  backup_before_change: true
  retention_days: 90
  compression: true
  encryption: true
  offsite_backup: true

# Production recovery settings
recovery_configuration:
  automatic_rollback: true
  manual_rollback_available: true
  point_in_time_recovery: true
  disaster_recovery_enabled: true
  rto_minutes: 30  # Recovery Time Objective
  rpo_minutes: 15  # Recovery Point Objective

# =========================
# Production Maintenance Configuration
# =========================

# Production maintenance windows
maintenance_windows:
  production:
    start_time: "02:00"
    end_time: "04:00"
    timezone: "Asia/Singapore"
    days: ["Sunday"]
    emergency_override: true

# Enhanced health checks for production
health_checks:
  enabled: true
  frequency_minutes: 5
  timeout_seconds: 30
  alert_on_failure: true
  escalation_on_repeated_failure: true

# =========================
# Production Feature Flags
# =========================

# Conservative feature flags for production
feature_flags:
  enhanced_logging: true
  performance_monitoring: true
  automatic_rollback: true
  real_time_validation: true
  bulk_operations: false  # Disabled for production safety
  api_integration: true
  web_interface: false

# Experimental features disabled in production
experimental_features:
  ai_powered_validation: false
  predictive_analytics: false
  auto_scaling: false
  multi_cloud_support: false

# =========================
# Production DNS Domain Configuration
# =========================

# Production-specific domain settings
dns_domain_mapping:
  "healthgrp.com.sg":
    primary_servers:
      - "HISADMTVPSEC11.healthgrp.com.sg"
    backup_servers: []
    environment: "production"
    security_zone: "main"
    change_approval_required: true

  "nhg.local":
    primary_servers:
      - "HISADMTVPSEC11.nhg.local"
    backup_servers: []
    environment: "production"
    security_zone: "nhg"
    change_approval_required: true

  "aic.local":
    primary_servers:
      - "AICADMTVPSEC11.aic.local"
    backup_servers: []
    environment: "production"
    security_zone: "aic"
    change_approval_required: true

  "iltc.healthgrp.com.sg":
    primary_servers:
      - "HISADMTVPSEC11.iltc.healthgrp.com.sg"
    backup_servers: []
    environment: "production"
    security_zone: "iltc"
    change_approval_required: true

  "shses.shs.com.sg":
    primary_servers:
      - "SHSADMTVPSEC12.shses.shs.com.sg"
    backup_servers: []
    environment: "production"
    security_zone: "singhealth"
    change_approval_required: true
