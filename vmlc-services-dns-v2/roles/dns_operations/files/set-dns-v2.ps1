# =========================================================================
# DNS Management PowerShell Script v2 - Enhanced Enterprise Solution
# =========================================================================
# This script handles DNS record operations with comprehensive error handling,
# logging, rollback capabilities, and security controls following OXAF
# infrastructure automation patterns.
#
# Features:
# - Enhanced error handling and rollback procedures
# - Comprehensive logging with standardized formats
# - Security controls and validation
# - Performance monitoring and metrics
# - Audit trail and compliance logging
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

param(
    [Parameter(Mandatory=$true)]
    [ValidateSet("verify", "add", "remove", "update", "sync")]
    [string]$Action,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$Domain,

    [Parameter(Mandatory=$true)]
    [ValidateNotNullOrEmpty()]
    [string]$HostName,

    [Parameter(Mandatory=$false)]
    [ValidatePattern("^(?:[0-9]{1,3}\.){3}[0-9]{1,3}$")]
    [string]$IpAddress,

    [Parameter(Mandatory=$false)]
    [ValidateRange(60, 86400)]
    [int]$TTL = 3600,

    [Parameter(Mandatory=$false)]
    [string]$LogFile,

    [Parameter(Mandatory=$false)]
    [string]$ExecutionId,

    [Parameter(Mandatory=$false)]
    [string]$JobId,

    [Parameter(Mandatory=$false)]
    [switch]$DryRun,

    [Parameter(Mandatory=$false)]
    [switch]$EnableRollback = $true,

    [Parameter(Mandatory=$false)]
    [switch]$Verbose
)

# =========================================================================
# Global Variables and Configuration
# =========================================================================

$script:ScriptVersion = "2.0"
$script:StartTime = Get-Date
$script:ExecutionId = if ($ExecutionId) { $ExecutionId } else { [System.Guid]::NewGuid().ToString() }
$script:JobId = if ($JobId) { $JobId } else { "LOCAL_EXECUTION" }
$script:LogFile = if ($LogFile) { $LogFile } else { "C:\OE_AAP_LOGS\DNS_SCRIPT_$($script:JobId)_$(Get-Date -Format 'ddMMyyyy').log" }

# Performance tracking
$script:PerformanceMetrics = @{
    StartTime = $script:StartTime
    EndTime = $null
    Duration = $null
    OperationsCount = 0
    ErrorsCount = 0
    WarningsCount = 0
}

# Rollback tracking
$script:RollbackData = @{
    Enabled = $EnableRollback.IsPresent
    Operations = @()
    BackupRecords = @()
}

# Security context
$script:SecurityContext = @{
    User = $env:USERNAME
    Domain = $env:USERDOMAIN
    Computer = $env:COMPUTERNAME
    ProcessId = $PID
    ExecutionPolicy = Get-ExecutionPolicy
}

# =========================================================================
# Logging Functions
# =========================================================================

function Write-EnhancedLog {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Message,

        [Parameter(Mandatory=$false)]
        [ValidateSet("INFO", "WARN", "ERROR", "DEBUG", "AUDIT")]
        [string]$Level = "INFO",

        [Parameter(Mandatory=$false)]
        [switch]$NoConsole
    )

    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff"
    $logEntry = "[$timestamp] [$Level] [ExecutionId:$($script:ExecutionId)] [JobId:$($script:JobId)] $Message"

    # Write to log file
    try {
        $logDir = Split-Path $script:LogFile -Parent
        if (-not (Test-Path $logDir)) {
            New-Item -Path $logDir -ItemType Directory -Force | Out-Null
        }
        Add-Content -Path $script:LogFile -Value $logEntry -Encoding UTF8
    } catch {
        Write-Warning "Failed to write to log file: $($_.Exception.Message)"
    }

    # Write to console if not suppressed
    if (-not $NoConsole) {
        switch ($Level) {
            "ERROR" { Write-Host $logEntry -ForegroundColor Red }
            "WARN"  { Write-Host $logEntry -ForegroundColor Yellow }
            "DEBUG" { if ($Verbose) { Write-Host $logEntry -ForegroundColor Cyan } }
            "AUDIT" { Write-Host $logEntry -ForegroundColor Magenta }
            default { Write-Host $logEntry -ForegroundColor White }
        }
    }
}

function Write-AuditLog {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Operation,

        [Parameter(Mandatory=$true)]
        [string]$Target,

        [Parameter(Mandatory=$true)]
        [string]$Result,

        [Parameter(Mandatory=$false)]
        [hashtable]$Details = @{}
    )

    $auditEntry = @{
        Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff"
        ExecutionId = $script:ExecutionId
        JobId = $script:JobId
        User = $script:SecurityContext.User
        Domain = $script:SecurityContext.Domain
        Computer = $script:SecurityContext.Computer
        Operation = $Operation
        Target = $Target
        Result = $Result
        Details = $Details
    }

    $auditJson = $auditEntry | ConvertTo-Json -Compress
    Write-EnhancedLog -Message "AUDIT: $auditJson" -Level "AUDIT"
}

# =========================================================================
# DNS Server Discovery Functions
# =========================================================================

function Get-DnsServerForDomain {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Domain
    )

    Write-EnhancedLog -Message "Discovering DNS server for domain: $Domain" -Level "INFO"

    # Enhanced domain-to-server mapping with environment awareness
    $domainServerMap = @{
        "devhealthgrp.com.sg" = @{
            Primary = "HISADMTVDSEC01.devhealthgrp.com.sg"
            Environment = "Development"
            SecurityZone = "Dev"
        }
        "healthgrpexts.com.sg" = @{
            Primary = "HISADMTVSSEC01.healthgrpexts.com.sg"
            Environment = "Staging"
            SecurityZone = "External"
        }
        "nnstg.local" = @{
            Primary = "HISADMTVSSEC02.nnstg.local"
            Environment = "Staging"
            SecurityZone = "Internal"
        }
        "ses.shsu.com.sg" = @{
            Primary = "SHSADMTVDSEC02.ses.shsu.com.sg"
            Environment = "Staging"
            SecurityZone = "SingHealth"
        }
        "shses.shs.com.sg" = @{
            Primary = "SHSADMTVPSEC12.shses.shs.com.sg"
            Environment = "Production"
            SecurityZone = "SingHealth"
        }
        "nhg.local" = @{
            Primary = "HISADMTVPSEC11.nhg.local"
            Environment = "Production"
            SecurityZone = "NHG"
        }
        "aic.local" = @{
            Primary = "AICADMTVPSEC11.aic.local"
            Environment = "Production"
            SecurityZone = "AIC"
        }
        "iltc.healthgrp.com.sg" = @{
            Primary = "HISADMTVPSEC11.iltc.healthgrp.com.sg"
            Environment = "Production"
            SecurityZone = "ILTC"
        }
        "healthgrp.com.sg" = @{
            Primary = "HISADMTVPSEC11.healthgrp.com.sg"
            Environment = "Production"
            SecurityZone = "Main"
        }
    }

    if ($domainServerMap.ContainsKey($Domain)) {
        $serverInfo = $domainServerMap[$Domain]
        Write-EnhancedLog -Message "DNS server found: $($serverInfo.Primary) (Environment: $($serverInfo.Environment), Zone: $($serverInfo.SecurityZone))" -Level "INFO"

        Write-AuditLog -Operation "DNS_SERVER_DISCOVERY" -Target $Domain -Result "SUCCESS" -Details @{
            Server = $serverInfo.Primary
            Environment = $serverInfo.Environment
            SecurityZone = $serverInfo.SecurityZone
        }

        return $serverInfo
    } else {
        Write-EnhancedLog -Message "No DNS server mapping found for domain: $Domain" -Level "ERROR"
        Write-AuditLog -Operation "DNS_SERVER_DISCOVERY" -Target $Domain -Result "FAILED" -Details @{
            Reason = "Domain not in mapping"
        }
        throw "Domain '$Domain' is not supported. Please check the domain name and try again."
    }
}

# =========================================================================
# DNS Record Validation Functions
# =========================================================================

function Test-DnsRecordExists {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Domain,

        [Parameter(Mandatory=$true)]
        [string]$HostName,

        [Parameter(Mandatory=$true)]
        [string]$DnsServer
    )

    Write-EnhancedLog -Message "Checking if DNS record exists: $HostName.$Domain" -Level "INFO"

    try {
        $fqdn = "$HostName.$Domain"

        # First check if the zone exists
        try {
            $zone = Get-DnsServerZone -Name $Domain -ComputerName $DnsServer -ErrorAction Stop
            Write-EnhancedLog -Message "Zone found: $($zone.ZoneName)" -Level "INFO"
        } catch {
            if ($_.Exception.Message -like "*zone*not*found*" -or $_.Exception.Message -like "*Zone*not*found*") {
                Write-EnhancedLog -Message "Zone not found: $Domain - DNS engineers need to create zone manually" -Level "WARN"
                Write-Host "ZONE_NOT_FOUND_WARNING: $Domain" -ForegroundColor Yellow
                Write-Host "DNS Zone '$Domain' not found - DNS engineers need to create zone manually" -ForegroundColor Yellow

                # Return a special result indicating zone not found
                $result = @{
                    Exists = $false
                    ARecord = $null
                    IpAddress = $null
                    TTL = $null
                    PtrExists = $false
                    PtrRecord = $null
                    ZoneNotFound = $true
                    ZoneName = $Domain
                }

                Write-AuditLog -Operation "DNS_ZONE_CHECK" -Target $Domain -Result "ZONE_NOT_FOUND" -Details $result
                return $result
            } else {
                # Re-throw other zone-related errors
                throw
            }
        }

        $record = Get-DnsServerResourceRecord -ZoneName $Domain -Name $HostName -RRType A -ComputerName $DnsServer -ErrorAction SilentlyContinue

        if ($record) {
            Write-EnhancedLog -Message "DNS A record found: $fqdn -> $($record.RecordData.IPv4Address)" -Level "INFO"

            # Check for PTR record using intelligent reverse zone detection
            $ptrExists = $false
            $ptrRecord = $null
            try {
                $ipAddress = $record.RecordData.IPv4Address.ToString()
                $reverseZoneInfo = Get-ReverseDomain -IpAddress $ipAddress -DnsServer $DnsServer

                if ($reverseZoneInfo.Found) {
                    $ptrRecord = Get-DnsServerResourceRecord -ZoneName $reverseZoneInfo.ZoneName -Name $reverseZoneInfo.PtrRecordName -RRType PTR -ComputerName $DnsServer -ErrorAction SilentlyContinue
                    $ptrExists = $ptrRecord -ne $null

                    if ($ptrExists) {
                        Write-EnhancedLog -Message "PTR record found in $($reverseZoneInfo.ZoneType) zone: $($reverseZoneInfo.PtrRecordName).$($reverseZoneInfo.ZoneName)" -Level "INFO"
                    } else {
                        Write-EnhancedLog -Message "PTR record not found in available reverse zone: $($reverseZoneInfo.ZoneName)" -Level "INFO"
                    }
                } else {
                    Write-EnhancedLog -Message "No reverse DNS zones available for PTR record check. Tested zones: $($reverseZoneInfo.TestedZones -join ', ')" -Level "WARN"
                }
            } catch {
                Write-EnhancedLog -Message "PTR record check failed: $($_.Exception.Message)" -Level "WARN"
            }

            $result = @{
                Exists = $true
                ARecord = $record
                IpAddress = $record.RecordData.IPv4Address.ToString()
                TTL = $record.TimeToLive.TotalSeconds
                PtrExists = $ptrExists
                PtrRecord = $ptrRecord
            }

            Write-AuditLog -Operation "DNS_RECORD_CHECK" -Target $fqdn -Result "EXISTS" -Details $result
            return $result
        } else {
            Write-EnhancedLog -Message "DNS A record not found: $fqdn" -Level "INFO"
            $result = @{
                Exists = $false
                ARecord = $null
                IpAddress = $null
                TTL = $null
                PtrExists = $false
                PtrRecord = $null
            }

            Write-AuditLog -Operation "DNS_RECORD_CHECK" -Target $fqdn -Result "NOT_EXISTS" -Details $result
            return $result
        }
    } catch {
        Write-EnhancedLog -Message "Error checking DNS record: $($_.Exception.Message)" -Level "ERROR"
        Write-AuditLog -Operation "DNS_RECORD_CHECK" -Target "$HostName.$Domain" -Result "ERROR" -Details @{
            Error = $_.Exception.Message
        }
        throw
    }
}

# =========================================================================
# Rollback Functions
# =========================================================================

function Add-RollbackOperation {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Operation,

        [Parameter(Mandatory=$true)]
        [hashtable]$Details
    )

    if ($script:RollbackData.Enabled) {
        $rollbackEntry = @{
            Timestamp = Get-Date
            Operation = $Operation
            Details = $Details
        }

        $script:RollbackData.Operations += $rollbackEntry
        Write-EnhancedLog -Message "Rollback operation recorded: $Operation" -Level "DEBUG"
    }
}

function Invoke-Rollback {
    param(
        [Parameter(Mandatory=$true)]
        [string]$Reason
    )

    if (-not $script:RollbackData.Enabled) {
        Write-EnhancedLog -Message "Rollback is disabled, skipping rollback operations" -Level "WARN"
        return
    }

    Write-EnhancedLog -Message "Starting rollback operations due to: $Reason" -Level "WARN"
    Write-AuditLog -Operation "ROLLBACK_START" -Target "ALL" -Result "INITIATED" -Details @{
        Reason = $Reason
        OperationsCount = $script:RollbackData.Operations.Count
    }

    # Reverse the order of operations for rollback
    $rollbackOperations = $script:RollbackData.Operations | Sort-Object Timestamp -Descending

    foreach ($operation in $rollbackOperations) {
        try {
            Write-EnhancedLog -Message "Rolling back operation: $($operation.Operation)" -Level "INFO"

            switch ($operation.Operation) {
                "ADD_A_RECORD" {
                    # Remove the A record that was added
                    Remove-DnsServerResourceRecord -ZoneName $operation.Details.Domain -Name $operation.Details.HostName -RRType A -Force -ComputerName $operation.Details.DnsServer
                    Write-EnhancedLog -Message "Rolled back A record: $($operation.Details.HostName).$($operation.Details.Domain)" -Level "INFO"
                }
                "ADD_PTR_RECORD" {
                    # Remove the PTR record that was added
                    Remove-DnsServerResourceRecord -ZoneName $operation.Details.ReverseDomain -Name $operation.Details.PtrName -RRType PTR -Force -ComputerName $operation.Details.DnsServer
                    Write-EnhancedLog -Message "Rolled back PTR record: $($operation.Details.PtrName)" -Level "INFO"
                }
                "REMOVE_A_RECORD" {
                    # Restore the A record that was removed
                    if ($operation.Details.BackupRecord) {
                        Add-DnsServerResourceRecordA -ZoneName $operation.Details.Domain -Name $operation.Details.HostName -IPv4Address $operation.Details.BackupRecord.IpAddress -TimeToLive (New-TimeSpan -Seconds $operation.Details.BackupRecord.TTL) -ComputerName $operation.Details.DnsServer
                        Write-EnhancedLog -Message "Restored A record: $($operation.Details.HostName).$($operation.Details.Domain)" -Level "INFO"
                    }
                }
                "REMOVE_PTR_RECORD" {
                    # Restore the PTR record that was removed
                    if ($operation.Details.BackupRecord) {
                        Add-DnsServerResourceRecordPtr -ZoneName $operation.Details.ReverseDomain -Name $operation.Details.PtrName -PtrDomainName $operation.Details.BackupRecord.PtrDomainName -ComputerName $operation.Details.DnsServer
                        Write-EnhancedLog -Message "Restored PTR record: $($operation.Details.PtrName)" -Level "INFO"
                    }
                }
            }

            Write-AuditLog -Operation "ROLLBACK_OPERATION" -Target $operation.Operation -Result "SUCCESS" -Details $operation.Details

        } catch {
            Write-EnhancedLog -Message "Failed to rollback operation $($operation.Operation): $($_.Exception.Message)" -Level "ERROR"
            Write-AuditLog -Operation "ROLLBACK_OPERATION" -Target $operation.Operation -Result "FAILED" -Details @{
                Error = $_.Exception.Message
                OriginalOperation = $operation.Details
            }
            $script:PerformanceMetrics.ErrorsCount++
        }
    }

    Write-EnhancedLog -Message "Rollback operations completed" -Level "INFO"
    Write-AuditLog -Operation "ROLLBACK_COMPLETE" -Target "ALL" -Result "COMPLETED" -Details @{
        Reason = $Reason
        OperationsProcessed = $rollbackOperations.Count
    }
}

# =========================================================================
# Helper Functions
# =========================================================================

function Get-ReverseDomain {
    param(
        [Parameter(Mandatory=$true)]
        [string]$IpAddress,

        [Parameter(Mandatory=$false)]
        [string]$DnsServer = "localhost"
    )

    $octets = $IpAddress.Split('.')
    if ($octets.Length -ne 4) {
        throw "Invalid IP address format: $IpAddress"
    }

    Write-EnhancedLog -Message "Starting intelligent reverse zone detection for IP: $IpAddress" -Level "INFO"

    # Define possible reverse zone configurations in order of preference (most specific to least specific)
    $reverseZoneOptions = @(
        @{
            Name = "$($octets[2]).$($octets[1]).$($octets[0]).in-addr.arpa"
            Type = "3-octet"
            Subnet = "/24"
            Description = "Class C subnet (most specific)"
            PtrNameCalculation = { param($ip) $ip.Split('.')[3] }
        },
        @{
            Name = "$($octets[1]).$($octets[0]).in-addr.arpa"
            Type = "2-octet"
            Subnet = "/16"
            Description = "Class B subnet"
            PtrNameCalculation = { param($ip) $octets = $ip.Split('.'); "$($octets[3]).$($octets[2])" }
        },
        @{
            Name = "$($octets[0]).in-addr.arpa"
            Type = "1-octet"
            Subnet = "/8"
            Description = "Class A subnet (least specific)"
            PtrNameCalculation = { param($ip) $octets = $ip.Split('.'); "$($octets[3]).$($octets[2]).$($octets[1])" }
        }
    )

    # Test each reverse zone option in order of preference
    foreach ($zoneOption in $reverseZoneOptions) {
        Write-EnhancedLog -Message "Testing reverse zone: $($zoneOption.Name) ($($zoneOption.Description))" -Level "INFO"

        try {
            # Check if the reverse zone exists
            $zone = Get-DnsServerZone -Name $zoneOption.Name -ComputerName $DnsServer -ErrorAction Stop

            if ($zone) {
                Write-EnhancedLog -Message "Found reverse zone: $($zoneOption.Name) (Type: $($zoneOption.Type), Subnet: $($zoneOption.Subnet))" -Level "INFO"

                # Calculate the correct PTR record name for this zone type
                $ptrRecordName = & $zoneOption.PtrNameCalculation $IpAddress

                # Return zone information with metadata
                return @{
                    ZoneName = $zoneOption.Name
                    ZoneType = $zoneOption.Type
                    SubnetMask = $zoneOption.Subnet
                    Description = $zoneOption.Description
                    PtrRecordName = $ptrRecordName
                    Found = $true
                }
            }
        } catch {
            Write-EnhancedLog -Message "Reverse zone not found: $($zoneOption.Name) - $($_.Exception.Message)" -Level "DEBUG"
            continue
        }
    }

    # If no reverse zones found, return error information
    Write-EnhancedLog -Message "No reverse DNS zones found for IP $IpAddress. Checked: 3-octet, 2-octet, and 1-octet zones" -Level "WARN"

    return @{
        ZoneName = $null
        ZoneType = "none"
        SubnetMask = "unknown"
        Description = "No reverse zone found"
        PtrRecordName = $null
        Found = $false
        TestedZones = $reverseZoneOptions | ForEach-Object { $_.Name }
    }
}

function Get-PtrRecordName {
    param(
        [Parameter(Mandatory=$true)]
        [string]$IpAddress,

        [Parameter(Mandatory=$false)]
        [string]$DnsServer = "localhost"
    )

    # Use the intelligent reverse zone detection to get the correct PTR record name
    $reverseZoneInfo = Get-ReverseDomain -IpAddress $IpAddress -DnsServer $DnsServer

    if ($reverseZoneInfo.Found) {
        Write-EnhancedLog -Message "PTR record name for IP $IpAddress in zone $($reverseZoneInfo.ZoneName): $($reverseZoneInfo.PtrRecordName)" -Level "INFO"
        return $reverseZoneInfo.PtrRecordName
    } else {
        # Fallback to traditional 3-octet calculation if no zones found
        $octets = $IpAddress.Split('.')
        $fallbackName = $octets[3]
        Write-EnhancedLog -Message "No reverse zones found, using fallback PTR name: $fallbackName" -Level "WARN"
        return $fallbackName
    }
}

# =========================================================================
# Main Execution Functions
# =========================================================================

function Initialize-Script {
    Write-EnhancedLog -Message "DNS Management Script v$($script:ScriptVersion) - Initialization Started" -Level "INFO"
    Write-EnhancedLog -Message "Execution ID: $($script:ExecutionId)" -Level "INFO"
    Write-EnhancedLog -Message "Job ID: $($script:JobId)" -Level "INFO"
    Write-EnhancedLog -Message "Parameters: Action=$Action, Domain=$Domain, HostName=$HostName, IpAddress=$IpAddress, TTL=$TTL" -Level "INFO"
    Write-EnhancedLog -Message "Security Context: User=$($script:SecurityContext.User), Domain=$($script:SecurityContext.Domain), Computer=$($script:SecurityContext.Computer)" -Level "INFO"
    Write-EnhancedLog -Message "Dry Run Mode: $($DryRun.IsPresent)" -Level "INFO"
    Write-EnhancedLog -Message "Rollback Enabled: $($script:RollbackData.Enabled)" -Level "INFO"

    Write-AuditLog -Operation "SCRIPT_INITIALIZATION" -Target "DNS_SCRIPT" -Result "SUCCESS" -Details @{
        Version = $script:ScriptVersion
        Parameters = @{
            Action = $Action
            Domain = $Domain
            HostName = $HostName
            IpAddress = $IpAddress
            TTL = $TTL
            DryRun = $DryRun.IsPresent
            RollbackEnabled = $script:RollbackData.Enabled
        }
        SecurityContext = $script:SecurityContext
    }
}

# =========================================================================
# Main Script Execution
# =========================================================================

try {
    # Initialize script
    Initialize-Script

    # Get DNS server information
    $serverInfo = Get-DnsServerForDomain -Domain $Domain
    $dnsServer = $serverInfo.Primary

    Write-EnhancedLog -Message "Using DNS server: $dnsServer for domain: $Domain" -Level "INFO"

    # Execute the requested action
    switch ($Action.ToLower()) {
        "verify" {
            Write-EnhancedLog -Message "Starting DNS record verification" -Level "INFO"
            $recordInfo = Test-DnsRecordExists -Domain $Domain -HostName $HostName -DnsServer $dnsServer

            if ($recordInfo.ZoneNotFound) {
                Write-Host "DNS Record Verification Result: ZONE NOT FOUND" -ForegroundColor Yellow
                Write-Host "DNS Zone: $Domain does not exist" -ForegroundColor Yellow
                Write-Host "Action Required: DNS engineers need to create zone '$Domain' manually" -ForegroundColor Yellow
                $script:PerformanceMetrics.WarningsCount++
            } elseif ($recordInfo.Exists) {
                Write-Host "DNS Record Verification Result: FOUND" -ForegroundColor Green
                Write-Host "A Record: $HostName.$Domain -> $($recordInfo.IpAddress)" -ForegroundColor Green
                Write-Host "TTL: $($recordInfo.TTL) seconds" -ForegroundColor Green
                Write-Host "PTR Record: $($recordInfo.PtrExists)" -ForegroundColor Green
            } else {
                Write-Host "DNS Record Verification Result: NOT FOUND" -ForegroundColor Yellow
                Write-Host "A Record: $HostName.$Domain does not exist" -ForegroundColor Yellow
            }

            $script:PerformanceMetrics.OperationsCount++
        }

        "add" {
            if (-not $IpAddress) {
                throw "IP address is required for add operation"
            }

            Write-EnhancedLog -Message "Starting DNS record addition" -Level "INFO"

            # Check if record already exists
            $existingRecord = Test-DnsRecordExists -Domain $Domain -HostName $HostName -DnsServer $dnsServer

            if ($existingRecord.ZoneNotFound) {
                Write-EnhancedLog -Message "Cannot add DNS record - zone not found: $Domain" -Level "WARN"
                Write-Host "DNS Record Addition Result: ZONE NOT FOUND" -ForegroundColor Yellow
                Write-Host "DNS Zone: $Domain does not exist" -ForegroundColor Yellow
                Write-Host "Action Required: DNS engineers need to create zone '$Domain' manually before adding records" -ForegroundColor Yellow
                $script:PerformanceMetrics.WarningsCount++
            } elseif ($existingRecord.Exists) {
                # Check if IP addresses match
                if ($existingRecord.IpAddress -eq $IpAddress) {
                    Write-EnhancedLog -Message "DNS A record already exists with same IP: $HostName.$Domain -> $($existingRecord.IpAddress)" -Level "INFO"
                    Write-Host "DNS Record Addition Result: A RECORD EXISTS" -ForegroundColor Green
                    Write-Host "Existing A Record: $HostName.$Domain -> $($existingRecord.IpAddress)" -ForegroundColor Green

                    # Check if PTR record exists and create if missing
                    if (-not $existingRecord.PtrExists) {
                        Write-EnhancedLog -Message "PTR record missing for existing A record, creating PTR record" -Level "INFO"
                        Write-Host "PTR Record: Missing - Creating PTR record" -ForegroundColor Yellow

                        if (-not $DryRun) {
                            try {
                                $reverseZoneInfo = Get-ReverseDomain -IpAddress $IpAddress -DnsServer $dnsServer
                                $fqdn = "$HostName.$Domain"

                                if ($reverseZoneInfo.Found) {
                                    Add-DnsServerResourceRecordPtr -ZoneName $reverseZoneInfo.ZoneName -Name $reverseZoneInfo.PtrRecordName -PtrDomainName $fqdn -ComputerName $dnsServer
                                    Write-EnhancedLog -Message "Added missing DNS PTR record: $($reverseZoneInfo.PtrRecordName).$($reverseZoneInfo.ZoneName) -> $fqdn (Zone Type: $($reverseZoneInfo.ZoneType))" -Level "INFO"
                                    Write-Host "PTR Record: Created successfully in $($reverseZoneInfo.ZoneType) zone" -ForegroundColor Green

                                    # Record rollback information for PTR only
                                    Add-RollbackOperation -Operation "ADD_PTR_RECORD" -Details @{
                                        ReverseDomain = $reverseZoneInfo.ZoneName
                                        PtrName = $reverseZoneInfo.PtrRecordName
                                        PtrDomainName = $fqdn
                                        ZoneType = $reverseZoneInfo.ZoneType
                                        DnsServer = $dnsServer
                                    }
                                } else {
                                    Write-EnhancedLog -Message "Cannot create PTR record - no reverse DNS zones found. Tested zones: $($reverseZoneInfo.TestedZones -join ', ')" -Level "WARN"
                                    Write-Host "PTR Record: Cannot create - no reverse DNS zones available" -ForegroundColor Yellow
                                    Write-Host "Tested zones: $($reverseZoneInfo.TestedZones -join ', ')" -ForegroundColor Yellow
                                    $script:PerformanceMetrics.WarningsCount++
                                }

                            } catch {
                                Write-EnhancedLog -Message "Failed to add missing PTR record: $($_.Exception.Message)" -Level "WARN"
                                Write-Host "PTR Record: Failed to create - $($_.Exception.Message)" -ForegroundColor Red
                                $script:PerformanceMetrics.WarningsCount++
                            }
                        } else {
                            Write-Host "DRY RUN: Would create missing PTR record for existing A record" -ForegroundColor Cyan
                        }
                    } else {
                        Write-Host "PTR Record: Already exists" -ForegroundColor Green
                    }
                } else {
                    Write-EnhancedLog -Message "DNS record exists with different IP: $HostName.$Domain -> $($existingRecord.IpAddress) (requested: $IpAddress)" -Level "WARN"
                    Write-Host "DNS Record Addition Result: IP MISMATCH" -ForegroundColor Red
                    Write-Host "Existing A Record: $HostName.$Domain -> $($existingRecord.IpAddress)" -ForegroundColor Red
                    Write-Host "Requested IP: $IpAddress" -ForegroundColor Red
                    Write-Host "Action Required: Use update operation to change IP address or verify the correct IP" -ForegroundColor Yellow
                }
            } else {
                if ($DryRun) {
                    Write-EnhancedLog -Message "DRY RUN: Would add DNS A record: $HostName.$Domain -> $IpAddress" -Level "INFO"
                    Write-Host "DRY RUN: Would add DNS A record: $HostName.$Domain -> $IpAddress" -ForegroundColor Cyan
                } else {
                    # Add A record
                    Add-DnsServerResourceRecordA -ZoneName $Domain -Name $HostName -IPv4Address $IpAddress -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $dnsServer
                    Write-EnhancedLog -Message "Added DNS A record: $HostName.$Domain -> $IpAddress" -Level "INFO"

                    # Record rollback information
                    Add-RollbackOperation -Operation "ADD_A_RECORD" -Details @{
                        Domain = $Domain
                        HostName = $HostName
                        IpAddress = $IpAddress
                        DnsServer = $dnsServer
                    }

                    # Add PTR record using intelligent reverse zone detection
                    try {
                        $reverseZoneInfo = Get-ReverseDomain -IpAddress $IpAddress -DnsServer $dnsServer
                        $fqdn = "$HostName.$Domain"

                        if ($reverseZoneInfo.Found) {
                            Add-DnsServerResourceRecordPtr -ZoneName $reverseZoneInfo.ZoneName -Name $reverseZoneInfo.PtrRecordName -PtrDomainName $fqdn -ComputerName $dnsServer
                            Write-EnhancedLog -Message "Added DNS PTR record: $($reverseZoneInfo.PtrRecordName).$($reverseZoneInfo.ZoneName) -> $fqdn (Zone Type: $($reverseZoneInfo.ZoneType))" -Level "INFO"
                            Write-Host "Added PTR Record: $($reverseZoneInfo.PtrRecordName).$($reverseZoneInfo.ZoneName) -> $fqdn" -ForegroundColor Green

                            # Record rollback information
                            Add-RollbackOperation -Operation "ADD_PTR_RECORD" -Details @{
                                ReverseDomain = $reverseZoneInfo.ZoneName
                                PtrName = $reverseZoneInfo.PtrRecordName
                                PtrDomainName = $fqdn
                                ZoneType = $reverseZoneInfo.ZoneType
                                DnsServer = $dnsServer
                            }
                        } else {
                            Write-EnhancedLog -Message "Cannot create PTR record - no reverse DNS zones found. Tested zones: $($reverseZoneInfo.TestedZones -join ', ')" -Level "WARN"
                            Write-Host "PTR Record: Cannot create - no reverse DNS zones available" -ForegroundColor Yellow
                            Write-Host "Tested zones: $($reverseZoneInfo.TestedZones -join ', ')" -ForegroundColor Yellow
                            $script:PerformanceMetrics.WarningsCount++
                        }

                    } catch {
                        Write-EnhancedLog -Message "Failed to add PTR record: $($_.Exception.Message)" -Level "WARN"
                        Write-Host "PTR Record: Failed to create - $($_.Exception.Message)" -ForegroundColor Red
                        $script:PerformanceMetrics.WarningsCount++
                    }

                    Write-Host "DNS Record Addition Result: SUCCESS" -ForegroundColor Green
                    Write-Host "Added A Record: $HostName.$Domain -> $IpAddress" -ForegroundColor Green
                }
            }

            $script:PerformanceMetrics.OperationsCount++
        }

        "remove" {
            Write-EnhancedLog -Message "Starting DNS record removal" -Level "INFO"

            # Check if record exists and backup if rollback is enabled
            $existingRecord = Test-DnsRecordExists -Domain $Domain -HostName $HostName -DnsServer $dnsServer

            if ($existingRecord.ZoneNotFound) {
                Write-EnhancedLog -Message "Cannot remove DNS record - zone not found: $Domain" -Level "WARN"
                Write-Host "DNS Record Removal Result: ZONE NOT FOUND" -ForegroundColor Yellow
                Write-Host "DNS Zone: $Domain does not exist" -ForegroundColor Yellow
                Write-Host "Action Required: DNS engineers need to create zone '$Domain' manually" -ForegroundColor Yellow
                $script:PerformanceMetrics.WarningsCount++
            } elseif (-not $existingRecord.Exists) {
                Write-EnhancedLog -Message "DNS record does not exist: $HostName.$Domain" -Level "WARN"
                Write-Host "DNS Record Removal Result: NOT FOUND" -ForegroundColor Yellow
                Write-Host "A Record: $HostName.$Domain does not exist" -ForegroundColor Yellow
            } else {
                if ($DryRun) {
                    Write-EnhancedLog -Message "DRY RUN: Would remove DNS A record: $HostName.$Domain" -Level "INFO"
                    Write-Host "DRY RUN: Would remove DNS A record: $HostName.$Domain -> $($existingRecord.IpAddress)" -ForegroundColor Cyan
                } else {
                    # Backup record for rollback
                    if ($script:RollbackData.Enabled) {
                        Add-RollbackOperation -Operation "REMOVE_A_RECORD" -Details @{
                            Domain = $Domain
                            HostName = $HostName
                            DnsServer = $dnsServer
                            BackupRecord = @{
                                IpAddress = $existingRecord.IpAddress
                                TTL = $existingRecord.TTL
                            }
                        }
                    }

                    # Remove A record
                    Remove-DnsServerResourceRecord -ZoneName $Domain -Name $HostName -RRType A -Force -ComputerName $dnsServer
                    Write-EnhancedLog -Message "Removed DNS A record: $HostName.$Domain" -Level "INFO"

                    # Remove PTR record if it exists using intelligent reverse zone detection
                    if ($existingRecord.PtrExists) {
                        try {
                            $reverseZoneInfo = Get-ReverseDomain -IpAddress $existingRecord.IpAddress -DnsServer $dnsServer

                            if ($reverseZoneInfo.Found) {
                                # Backup PTR record for rollback
                                if ($script:RollbackData.Enabled) {
                                    Add-RollbackOperation -Operation "REMOVE_PTR_RECORD" -Details @{
                                        ReverseDomain = $reverseZoneInfo.ZoneName
                                        PtrName = $reverseZoneInfo.PtrRecordName
                                        ZoneType = $reverseZoneInfo.ZoneType
                                        DnsServer = $dnsServer
                                        BackupRecord = @{
                                            PtrDomainName = $existingRecord.PtrRecord.RecordData.PtrDomainName
                                        }
                                    }
                                }

                                Remove-DnsServerResourceRecord -ZoneName $reverseZoneInfo.ZoneName -Name $reverseZoneInfo.PtrRecordName -RRType PTR -Force -ComputerName $dnsServer
                                Write-EnhancedLog -Message "Removed DNS PTR record: $($reverseZoneInfo.PtrRecordName).$($reverseZoneInfo.ZoneName) (Zone Type: $($reverseZoneInfo.ZoneType))" -Level "INFO"
                                Write-Host "Removed PTR Record: $($reverseZoneInfo.PtrRecordName).$($reverseZoneInfo.ZoneName)" -ForegroundColor Green
                            } else {
                                Write-EnhancedLog -Message "Cannot remove PTR record - no reverse DNS zones found. Tested zones: $($reverseZoneInfo.TestedZones -join ', ')" -Level "WARN"
                                Write-Host "PTR Record: Cannot remove - no reverse DNS zones available" -ForegroundColor Yellow
                                $script:PerformanceMetrics.WarningsCount++
                            }

                        } catch {
                            Write-EnhancedLog -Message "Failed to remove PTR record: $($_.Exception.Message)" -Level "WARN"
                            Write-Host "PTR Record: Failed to remove - $($_.Exception.Message)" -ForegroundColor Red
                            $script:PerformanceMetrics.WarningsCount++
                        }
                    }

                    Write-Host "DNS Record Removal Result: SUCCESS" -ForegroundColor Green
                    Write-Host "Removed A Record: $HostName.$Domain" -ForegroundColor Green
                }
            }

            $script:PerformanceMetrics.OperationsCount++
        }

        "sync" {
            Write-EnhancedLog -Message "Starting DNS record synchronization" -Level "INFO"

            # Check current state of DNS records
            $recordInfo = Test-DnsRecordExists -Domain $Domain -HostName $HostName -DnsServer $dnsServer

            if ($recordInfo.ZoneNotFound) {
                Write-EnhancedLog -Message "Cannot sync DNS records - zone not found: $Domain" -Level "WARN"
                Write-Host "DNS Record Sync Result: ZONE NOT FOUND" -ForegroundColor Yellow
                Write-Host "DNS Zone: $Domain does not exist" -ForegroundColor Yellow
                Write-Host "Action Required: DNS engineers need to create zone '$Domain' manually before sync operations" -ForegroundColor Yellow
                $script:PerformanceMetrics.WarningsCount++
            } else {
                # Determine what needs to be synchronized
                $syncActions = @()

                if (-not $recordInfo.Exists -and $IpAddress) {
                    # A record doesn't exist, create both A and PTR
                    $syncActions += "CREATE_A_RECORD"
                    $syncActions += "CREATE_PTR_RECORD"
                } elseif ($recordInfo.Exists -and $IpAddress) {
                    # A record exists, check if IP matches and PTR exists
                    if ($recordInfo.IpAddress -eq $IpAddress) {
                        if (-not $recordInfo.PtrExists) {
                            $syncActions += "CREATE_PTR_RECORD"
                        }
                    } else {
                        # IP mismatch, update both records
                        $syncActions += "UPDATE_A_RECORD"
                        $syncActions += "UPDATE_PTR_RECORD"
                    }
                } elseif ($recordInfo.Exists -and -not $IpAddress) {
                    # A record exists but no IP provided, just check PTR
                    if (-not $recordInfo.PtrExists) {
                        $syncActions += "CREATE_PTR_RECORD"
                        $IpAddress = $recordInfo.IpAddress  # Use existing IP
                    }
                }

                Write-Host "DNS Record Sync Analysis:" -ForegroundColor Cyan
                Write-Host "A Record Status: $($recordInfo.Exists ? 'EXISTS' : 'MISSING')" -ForegroundColor Cyan
                Write-Host "PTR Record Status: $($recordInfo.PtrExists ? 'EXISTS' : 'MISSING')" -ForegroundColor Cyan
                Write-Host "Sync Actions Required: $($syncActions.Count)" -ForegroundColor Cyan

                if ($syncActions.Count -eq 0) {
                    Write-Host "DNS Record Sync Result: NO ACTION NEEDED" -ForegroundColor Green
                    Write-Host "Both A and PTR records are properly synchronized" -ForegroundColor Green
                } else {
                    Write-Host "DNS Record Sync Result: SYNCHRONIZATION NEEDED" -ForegroundColor Yellow

                    foreach ($action in $syncActions) {
                        if ($DryRun) {
                            Write-Host "DRY RUN: Would execute sync action: $action" -ForegroundColor Cyan
                        } else {
                            try {
                                switch ($action) {
                                    "CREATE_A_RECORD" {
                                        Add-DnsServerResourceRecordA -ZoneName $Domain -Name $HostName -IPv4Address $IpAddress -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $dnsServer
                                        Write-EnhancedLog -Message "Sync: Created DNS A record: $HostName.$Domain -> $IpAddress" -Level "INFO"
                                        Write-Host "Sync: Created A Record: $HostName.$Domain -> $IpAddress" -ForegroundColor Green

                                        Add-RollbackOperation -Operation "ADD_A_RECORD" -Details @{
                                            Domain = $Domain
                                            HostName = $HostName
                                            IpAddress = $IpAddress
                                            DnsServer = $dnsServer
                                        }
                                    }
                                    "CREATE_PTR_RECORD" {
                                        $reverseZoneInfo = Get-ReverseDomain -IpAddress $IpAddress -DnsServer $dnsServer
                                        $fqdn = "$HostName.$Domain"

                                        if ($reverseZoneInfo.Found) {
                                            Add-DnsServerResourceRecordPtr -ZoneName $reverseZoneInfo.ZoneName -Name $reverseZoneInfo.PtrRecordName -PtrDomainName $fqdn -ComputerName $dnsServer
                                            Write-EnhancedLog -Message "Sync: Created DNS PTR record: $($reverseZoneInfo.PtrRecordName).$($reverseZoneInfo.ZoneName) -> $fqdn (Zone Type: $($reverseZoneInfo.ZoneType))" -Level "INFO"
                                            Write-Host "Sync: Created PTR Record: $($reverseZoneInfo.PtrRecordName).$($reverseZoneInfo.ZoneName) -> $fqdn" -ForegroundColor Green

                                            Add-RollbackOperation -Operation "ADD_PTR_RECORD" -Details @{
                                                ReverseDomain = $reverseZoneInfo.ZoneName
                                                PtrName = $reverseZoneInfo.PtrRecordName
                                                PtrDomainName = $fqdn
                                                ZoneType = $reverseZoneInfo.ZoneType
                                                DnsServer = $dnsServer
                                            }
                                        } else {
                                            Write-EnhancedLog -Message "Sync: Cannot create PTR record - no reverse DNS zones found. Tested zones: $($reverseZoneInfo.TestedZones -join ', ')" -Level "WARN"
                                            Write-Host "Sync: PTR Record creation failed - no reverse DNS zones available" -ForegroundColor Yellow
                                            throw "No reverse DNS zones available for PTR record creation"
                                        }
                                    }
                                    "UPDATE_A_RECORD" {
                                        # Remove old A record and create new one
                                        Remove-DnsServerResourceRecord -ZoneName $Domain -Name $HostName -RRType A -Force -ComputerName $dnsServer
                                        Add-DnsServerResourceRecordA -ZoneName $Domain -Name $HostName -IPv4Address $IpAddress -TimeToLive (New-TimeSpan -Seconds $TTL) -ComputerName $dnsServer
                                        Write-EnhancedLog -Message "Sync: Updated DNS A record: $HostName.$Domain -> $IpAddress" -Level "INFO"
                                        Write-Host "Sync: Updated A Record: $HostName.$Domain -> $IpAddress" -ForegroundColor Green
                                    }
                                    "UPDATE_PTR_RECORD" {
                                        # Remove old PTR record and create new one using intelligent zone detection
                                        $oldReverseZoneInfo = Get-ReverseDomain -IpAddress $recordInfo.IpAddress -DnsServer $dnsServer
                                        if ($oldReverseZoneInfo.Found) {
                                            Remove-DnsServerResourceRecord -ZoneName $oldReverseZoneInfo.ZoneName -Name $oldReverseZoneInfo.PtrRecordName -RRType PTR -Force -ComputerName $dnsServer
                                            Write-EnhancedLog -Message "Sync: Removed old PTR record: $($oldReverseZoneInfo.PtrRecordName).$($oldReverseZoneInfo.ZoneName)" -Level "INFO"
                                        }

                                        $newReverseZoneInfo = Get-ReverseDomain -IpAddress $IpAddress -DnsServer $dnsServer
                                        $fqdn = "$HostName.$Domain"

                                        if ($newReverseZoneInfo.Found) {
                                            Add-DnsServerResourceRecordPtr -ZoneName $newReverseZoneInfo.ZoneName -Name $newReverseZoneInfo.PtrRecordName -PtrDomainName $fqdn -ComputerName $dnsServer
                                            Write-EnhancedLog -Message "Sync: Updated DNS PTR record: $($newReverseZoneInfo.PtrRecordName).$($newReverseZoneInfo.ZoneName) -> $fqdn (Zone Type: $($newReverseZoneInfo.ZoneType))" -Level "INFO"
                                            Write-Host "Sync: Updated PTR Record: $($newReverseZoneInfo.PtrRecordName).$($newReverseZoneInfo.ZoneName) -> $fqdn" -ForegroundColor Green
                                        } else {
                                            Write-EnhancedLog -Message "Sync: Cannot create new PTR record - no reverse DNS zones found for new IP. Tested zones: $($newReverseZoneInfo.TestedZones -join ', ')" -Level "WARN"
                                            Write-Host "Sync: PTR Record update failed - no reverse DNS zones available for new IP" -ForegroundColor Yellow
                                            throw "No reverse DNS zones available for new PTR record creation"
                                        }
                                    }
                                }
                            } catch {
                                Write-EnhancedLog -Message "Sync action failed ($action): $($_.Exception.Message)" -Level "ERROR"
                                Write-Host "Sync: Failed to execute $action - $($_.Exception.Message)" -ForegroundColor Red
                                $script:PerformanceMetrics.ErrorsCount++
                            }
                        }
                    }

                    if (-not $DryRun) {
                        Write-Host "DNS Record Sync Result: COMPLETED" -ForegroundColor Green
                        Write-Host "Executed $($syncActions.Count) synchronization action(s)" -ForegroundColor Green
                    }
                }
            }

            $script:PerformanceMetrics.OperationsCount++
        }

        default {
            throw "Invalid action specified: $Action. Valid actions are: verify, add, remove, update, sync"
        }
    }

    # Calculate performance metrics
    $script:PerformanceMetrics.EndTime = Get-Date
    $script:PerformanceMetrics.Duration = $script:PerformanceMetrics.EndTime - $script:PerformanceMetrics.StartTime

    Write-EnhancedLog -Message "DNS operation completed successfully" -Level "INFO"
    Write-EnhancedLog -Message "Performance: Duration=$($script:PerformanceMetrics.Duration.TotalSeconds)s, Operations=$($script:PerformanceMetrics.OperationsCount), Errors=$($script:PerformanceMetrics.ErrorsCount), Warnings=$($script:PerformanceMetrics.WarningsCount)" -Level "INFO"

    Write-AuditLog -Operation "SCRIPT_COMPLETION" -Target "DNS_SCRIPT" -Result "SUCCESS" -Details @{
        Action = $Action
        Domain = $Domain
        HostName = $HostName
        PerformanceMetrics = $script:PerformanceMetrics
        RollbackOperations = $script:RollbackData.Operations.Count
    }

    exit 0

} catch {
    $script:PerformanceMetrics.ErrorsCount++
    $script:PerformanceMetrics.EndTime = Get-Date
    $script:PerformanceMetrics.Duration = $script:PerformanceMetrics.EndTime - $script:PerformanceMetrics.StartTime

    Write-EnhancedLog -Message "DNS operation failed: $($_.Exception.Message)" -Level "ERROR"
    Write-EnhancedLog -Message "Stack trace: $($_.ScriptStackTrace)" -Level "DEBUG"

    Write-AuditLog -Operation "SCRIPT_ERROR" -Target "DNS_SCRIPT" -Result "FAILED" -Details @{
        Error = $_.Exception.Message
        StackTrace = $_.ScriptStackTrace
        PerformanceMetrics = $script:PerformanceMetrics
    }

    # Attempt rollback if enabled and not in dry run mode
    if ($script:RollbackData.Enabled -and -not $DryRun -and $script:RollbackData.Operations.Count -gt 0) {
        try {
            Invoke-Rollback -Reason "Script execution failed: $($_.Exception.Message)"
        } catch {
            Write-EnhancedLog -Message "Rollback failed: $($_.Exception.Message)" -Level "ERROR"
        }
    }

    Write-Host "DNS operation failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
