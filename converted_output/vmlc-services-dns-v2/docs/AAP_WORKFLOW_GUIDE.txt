# AAP Workflow Guide - DNS Management Automation v2

## Step-by-Step AAP Execution Guide

**Framework:** Operational Excellence Automation Framework (OXAF)
**Version:** 2.0
**Purpose:** Practical guide for executing DNS operations through AAP

---

## 🎯 **AAP Job Template Setup**

### **Job Template Configuration**
```yaml
Name: DNS Management v2 - Production
Job Type: Run
Inventory: DNS Servers - Production
Project: DNS Management v2
Playbook: main.yml
Credentials:
  - CyberArk Credential Provider
  - DNS Service Accounts
Variables:
  var_environment: production
Survey Enabled: Yes
Extra Variables Enabled: Yes
```

---

## 📋 **Step-by-Step Execution Process**

### **Step 1: Access AAP Interface**
1. **Login to AAP:** Navigate to your AAP instance
2. **Locate Job Template:** Find "DNS Management v2 - Production"
3. **Click Launch:** Click the rocket icon to launch the job

### **Step 2: Survey Form vs Extra Variables**

#### **Option A: Using Survey Form (Recommended for beginners)**
- Fill out the survey form fields:
  - DNS Action: Select from dropdown
  - Domain Name: Enter target domain
  - Hostname: Enter hostname
  - IP Address: Enter IP (if required)
  - Service Request Number: Enter SR number
  - Enable Testing Mode: Check for dry run

#### **Option B: Using Extra Variables - JSON Format (Advanced users)**
- Skip survey and use "Extra Variables" field
- Paste JSON configuration directly
- More flexible for complex operations

#### **Option C: Using Extra Variables - YAML Format (Ansible-native)**
- Skip survey and use "Extra Variables" field
- Paste YAML configuration directly
- More readable for complex configurations
- Supports comments and documentation

---

## 🚀 **Practical Execution Examples**

### **Example 1: Adding a New Web Server**

#### **Scenario:**
- New web server deployment
- Hostname: web03
- IP Address: *************
- Domain: healthgrp.com.sg
- Service Request: SR-WEB-003

#### **AAP Extra Variables (JSON Format):**
```json
{
  "var_action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "web03",
  "ipaddress": "*************",
  "ttl": 3600,
  "var_sr_number": "SR-WEB-003",
  "testing_mode": false,
  "execution_context": {
    "purpose": "Web server deployment",
    "team": "Infrastructure Team",
    "operator": "<EMAIL>"
  }
}
```

#### **AAP Extra Variables (YAML Format):**
```yaml
var_action: add
domain: healthgrp.com.sg
hostname: web03
ipaddress: *************
ttl: 3600
var_sr_number: SR-WEB-003
testing_mode: false
execution_context:
  purpose: Web server deployment
  team: Infrastructure Team
  operator: <EMAIL>
```

#### **Expected AAP Output:**
```
PLAY [DNS Management Automation v2] ************************************

TASK [Phase 1: Configuration] ******************************************
ok: [HISADMTVPSEC11]

TASK [Phase 2: Loading] *************************************************
ok: [HISADMTVPSEC11]

TASK [Phase 3: Execution] **********************************************
changed: [HISADMTVPSEC11]

TASK [Phase 4: Error Handling] *****************************************
ok: [HISADMTVPSEC11]

TASK [Phase 5: Reporting] **********************************************
ok: [HISADMTVPSEC11]

TASK [Phase 6: Cleanup] ************************************************
ok: [HISADMTVPSEC11]

PLAY RECAP **************************************************************
HISADMTVPSEC11             : ok=6    changed=1    unreachable=0    failed=0
```

### **Example 2: Server Migration (IP Update)**

#### **Scenario:**
- Database server migration
- Hostname: db01 (existing)
- New IP Address: ************
- Domain: healthgrp.com.sg
- Service Request: SR-DB-MIGRATE-001

#### **AAP Extra Variables (JSON Format):**
```json
{
  "var_action": "update",
  "domain": "healthgrp.com.sg",
  "hostname": "db01",
  "ipaddress": "************",
  "ttl": 7200,
  "var_sr_number": "SR-DB-MIGRATE-001",
  "testing_mode": false,
  "enable_rollback": true,
  "execution_context": {
    "purpose": "Database server migration",
    "maintenance_window": "2024-01-15 02:00:00 SGT",
    "approval_reference": "CHANGE-***********"
  }
}
```

#### **AAP Extra Variables (YAML Format):**
```yaml
var_action: update
domain: healthgrp.com.sg
hostname: db01
ipaddress: ************
ttl: 7200
var_sr_number: SR-DB-MIGRATE-001
testing_mode: false
enable_rollback: true
execution_context:
  purpose: Database server migration
  maintenance_window: "2024-01-15 02:00:00 SGT"
  approval_reference: CHANGE-***********
```

### **Example 3: Server Decommissioning**

#### **Scenario:**
- Legacy server removal
- Hostname: legacy01
- Domain: healthgrp.com.sg
- Service Request: SR-DECOM-001

#### **AAP Extra Variables (JSON Format):**
```json
{
  "var_action": "remove",
  "domain": "healthgrp.com.sg",
  "hostname": "legacy01",
  "var_sr_number": "SR-DECOM-001",
  "testing_mode": false,
  "execution_context": {
    "purpose": "Server decommissioning",
    "approval_reference": "CHANGE-***********",
    "decommission_date": "2024-01-20"
  }
}
```

#### **AAP Extra Variables (YAML Format):**
```yaml
var_action: remove
domain: healthgrp.com.sg
hostname: legacy01
var_sr_number: SR-DECOM-001
testing_mode: false
execution_context:
  purpose: Server decommissioning
  approval_reference: CHANGE-***********
  decommission_date: "2024-01-20"
```

### **Example 4: Post-Zone Creation Sync**

#### **Scenario:**
- DNS engineers created new zone
- Zone: newapp.healthgrp.com.sg
- Hostname: app01
- IP Address: *************
- Service Request: SR-ZONE-SYNC-001

#### **AAP Extra Variables (JSON Format):**
```json
{
  "var_action": "sync",
  "domain": "newapp.healthgrp.com.sg",
  "hostname": "app01",
  "ipaddress": "*************",
  "ttl": 3600,
  "var_sr_number": "SR-ZONE-SYNC-001",
  "testing_mode": false,
  "execution_context": {
    "purpose": "Post-zone creation synchronization",
    "zone_created_by": "DNS Engineering Team",
    "zone_creation_ticket": "TASK-DNS-001"
  }
}
```

#### **AAP Extra Variables (YAML Format):**
```yaml
var_action: sync
domain: newapp.healthgrp.com.sg
hostname: app01
ipaddress: *************
ttl: 3600
var_sr_number: SR-ZONE-SYNC-001
testing_mode: false
execution_context:
  purpose: Post-zone creation synchronization
  zone_created_by: DNS Engineering Team
  zone_creation_ticket: TASK-DNS-001
```

---

## 🧪 **Testing Workflow**

### **Development Environment Testing**

#### **Step 1: Test in Development**

**JSON Format:**
```json
{
  "var_action": "add",
  "domain": "devhealthgrp.com.sg",
  "hostname": "testserver01",
  "ipaddress": "**********",
  "ttl": 300,
  "var_environment": "development",
  "testing_mode": true,
  "var_sr_number": "SR-TEST-001"
}
```

**YAML Format:**
```yaml
var_action: add
domain: devhealthgrp.com.sg
hostname: testserver01
ipaddress: **********
ttl: 300
var_environment: development
testing_mode: true
var_sr_number: SR-TEST-001
```

#### **Step 2: Validate Results**

**JSON Format:**
```json
{
  "var_action": "verify",
  "domain": "devhealthgrp.com.sg",
  "hostname": "testserver01",
  "var_environment": "development",
  "var_sr_number": "SR-TEST-001"
}
```

**YAML Format:**
```yaml
var_action: verify
domain: devhealthgrp.com.sg
hostname: testserver01
var_environment: development
var_sr_number: SR-TEST-001
```

#### **Step 3: Production Deployment**

**JSON Format:**
```json
{
  "var_action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "prodserver01",
  "ipaddress": "*************",
  "ttl": 3600,
  "var_environment": "production",
  "testing_mode": false,
  "var_sr_number": "SR-PROD-001"
}
```

**YAML Format:**
```yaml
var_action: add
domain: healthgrp.com.sg
hostname: prodserver01
ipaddress: *************
ttl: 3600
var_environment: production
testing_mode: false
var_sr_number: SR-PROD-001
```

---

## 📊 **Monitoring and Validation**

### **Job Execution Monitoring**

#### **Real-time Monitoring:**
1. **Job Status:** Monitor job progress in AAP interface
2. **Live Output:** Watch real-time execution logs
3. **Task Progress:** Track individual task completion

#### **Post-Execution Validation:**
1. **Job Results:** Review final job status and output
2. **DNS Verification:** Validate DNS records were created/modified
3. **Log Analysis:** Review detailed execution logs

### **Validation Commands**
```bash
# Verify A record
nslookup web03.healthgrp.com.sg

# Verify PTR record
nslookup *************

# Check DNS propagation
dig web03.healthgrp.com.sg @*******
```

---

## 🔧 **Troubleshooting Common Issues**

### **Issue 1: JSON Syntax Error**
**Symptom:** Job fails with "Extra variables must be valid JSON"
**Solution:**
1. Validate JSON syntax using online validator
2. Check for missing quotes, commas, or brackets
3. Ensure proper boolean values (true/false)

**Corrected Example:**
```json
{
  "var_action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "server01",
  "ipaddress": "*************",
  "testing_mode": false
}
```

### **Issue 2: Missing Required Variables**
**Symptom:** Job fails with "Missing required variable"
**Solution:**
1. Check operation-specific requirements
2. Ensure all mandatory fields are provided
3. Verify variable names match exactly

**Required Variables by Operation:**
- **verify:** action, domain, hostname, var_sr_number
- **add:** action, domain, hostname, ipaddress, var_sr_number
- **remove:** action, domain, hostname, var_sr_number
- **update:** action, domain, hostname, ipaddress, var_sr_number
- **sync:** action, domain, hostname, var_sr_number

### **Issue 3: Zone Not Found**
**Symptom:** Warning about zone not found
**Expected Behavior:** Operation logs warning but continues
**Follow-up Action:**
1. Contact DNS engineers to create zone
2. Use sync operation after zone creation
3. Monitor email notifications for zone creation requests

### **Issue 4: Permission Denied**
**Symptom:** DNS operations fail with permission errors
**Solution:**
1. Verify CyberArk credentials are working
2. Check DNS service account permissions
3. Validate target DNS server accessibility

---

## 📋 **Best Practices for AAP Execution**

### **Pre-Execution Checklist**
- ✅ Validate JSON syntax before execution
- ✅ Verify service request number format
- ✅ Confirm target environment selection
- ✅ Enable testing mode for initial validation
- ✅ Review execution context information

### **During Execution**
- 👀 Monitor job progress in real-time
- 📝 Note any warnings or unusual output
- ⏱️ Track execution time for performance baseline
- 🔍 Watch for safety check warnings

### **Post-Execution**
- ✅ Verify DNS records were created/modified correctly
- 📧 Check for email notifications (if any warnings)
- 📋 Update service request with execution results
- 🔄 Test DNS resolution from multiple locations

### **Documentation Requirements**
- 📝 Record execution details in service request
- 📊 Document any warnings or issues encountered
- 🔗 Link AAP job execution ID to service request
- 📅 Note execution timestamp and operator

---

## 🔄 **JSON vs YAML Format Selection**

### **When to Use JSON Format**
- ✅ **Simple Operations:** Basic add/remove/verify operations
- ✅ **API Integration:** When integrating with external systems
- ✅ **Compact Representation:** When space is limited
- ✅ **Quick Copy-Paste:** For rapid execution

### **When to Use YAML Format**
- ✅ **Complex Configurations:** Multi-server or bulk operations
- ✅ **Team Collaboration:** When readability is important
- ✅ **Documentation:** When inline comments are needed
- ✅ **Ansible-Native:** For Ansible-centric workflows

### **Format Comparison Example**

#### **JSON Format (Compact):**
```json
{"var_action":"add","domain":"healthgrp.com.sg","hostname":"web01","ipaddress":"*************","ttl":3600,"var_sr_number":"SR-WEB-001","testing_mode":false}
```

#### **YAML Format (Readable):**
```yaml
# Web server deployment for customer portal
var_action: add
domain: healthgrp.com.sg
hostname: web01
ipaddress: *************
ttl: 3600  # Standard production TTL
var_sr_number: SR-WEB-001
testing_mode: false
```

---

## 🎯 **Quick Reference Commands**

### **JSON Templates (One-Line)**

#### **Quick Add (Production):**
```json
{"var_action":"add","domain":"healthgrp.com.sg","hostname":"HOSTNAME","ipaddress":"IP_ADDRESS","ttl":3600,"var_sr_number":"SR_NUMBER","testing_mode":false}
```

#### **Quick Verify:**
```json
{"var_action":"verify","domain":"healthgrp.com.sg","hostname":"HOSTNAME","var_sr_number":"SR_NUMBER","testing_mode":false}
```

#### **Quick Remove:**
```json
{"var_action":"remove","domain":"healthgrp.com.sg","hostname":"HOSTNAME","var_sr_number":"SR_NUMBER","testing_mode":false}
```

#### **Quick Sync:**
```json
{"var_action":"sync","domain":"healthgrp.com.sg","hostname":"HOSTNAME","var_sr_number":"SR_NUMBER","testing_mode":false}
```

### **YAML Templates (Readable)**

#### **Quick Add (Production):**
```yaml
var_action: add
domain: healthgrp.com.sg
hostname: HOSTNAME
ipaddress: IP_ADDRESS
ttl: 3600
var_sr_number: SR_NUMBER
testing_mode: false
```

#### **Quick Verify:**
```yaml
var_action: verify
domain: healthgrp.com.sg
hostname: HOSTNAME
var_sr_number: SR_NUMBER
testing_mode: false
```

#### **Quick Remove:**
```yaml
var_action: remove
domain: healthgrp.com.sg
hostname: HOSTNAME
var_sr_number: SR_NUMBER
testing_mode: false
```

#### **Quick Sync:**
```yaml
var_action: sync
domain: healthgrp.com.sg
hostname: HOSTNAME
var_sr_number: SR_NUMBER
testing_mode: false
```

---

## 📞 **Support and Escalation**

### **Level 1 Support (Self-Service)**
- Review this workflow guide
- Validate JSON syntax
- Check common troubleshooting steps
- Retry with testing mode enabled

### **Level 2 Support (Team Assistance)**
- Contact DNS Automation Team
- Provide AAP job execution ID
- Include service request number
- Share JSON configuration used

### **Level 3 Support (Emergency)**
- Critical production issues
- DNS service outages
- Escalate through ITSM
- Include all execution details

---

*This AAP workflow guide is part of the DNS Management Automation v2 solution developed by the CES Operational Excellence Team following Operational Excellence Automation Framework (OXAF) patterns.*
