---
# =========================================================================
# DNS Lifecycle - Phase Error Handler
# =========================================================================
# This task handles errors that occur during any phase of the DNS lifecycle
# and provides standardized error processing, logging, and recovery procedures.
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

- name: "Phase Error Handler - Initialize"
  debug:
    msg: |
      DNS Lifecycle Phase Error Handler
      ================================
      Failed Phase: {{ failed_phase | default('unknown') }}
      Error Message: {{ error_message | default('No error message provided') }}
      Execution ID: {{ execution_context.id | default('unknown') }}
      Job ID: {{ execution_context.job_id | default('unknown') }}
      Timestamp: {{ ansible_date_time.iso8601 }}

- name: "Classify Error Severity"
  set_fact:
    error_classification:
      phase: "{{ failed_phase | default('unknown') }}"
      message: "{{ error_message | default('Unknown error occurred') }}"
      severity: "{{ 'warning' if 'zone_not_found' in error_message or 'Zone not found' in error_message else 'critical' if failed_phase in ['configuration', 'loading'] else 'high' if failed_phase == 'execution' else 'medium' }}"
      category: "{{ 'dns_configuration' if 'zone_not_found' in error_message or 'Zone not found' in error_message else 'infrastructure' if 'server' in error_message or 'network' in error_message else 'authentication' if 'credential' in error_message or 'auth' in error_message else 'application' }}"
      timestamp: "{{ ansible_date_time.iso8601 }}"
      execution_context: "{{ execution_context | default({}) }}"

- name: "Log Error Classification"
  debug:
    msg: |
      Error Classification:
      ====================
      Severity: {{ error_classification.severity | upper }}
      Category: {{ error_classification.category | upper }}
      Phase: {{ error_classification.phase | upper }}
      Message: {{ error_classification.message }}

- name: "Determine Recovery Actions"
  set_fact:
    recovery_actions:
      rollback_required: "{{ error_classification.severity in ['critical', 'high'] and error_classification.category != 'dns_configuration' }}"
      notification_required: true
      escalation_required: "{{ error_classification.severity == 'critical' }}"
      manual_intervention_required: "{{ error_classification.category == 'dns_configuration' or (error_classification.category == 'infrastructure' and error_classification.severity == 'critical') }}"
      retry_possible: "{{ error_classification.category in ['network', 'application'] and error_classification.severity != 'critical' }}"
      dns_engineer_notification_required: "{{ error_classification.category == 'dns_configuration' }}"

- name: "Log Recovery Actions"
  debug:
    msg: |
      Recovery Actions Determined:
      ===========================
      Rollback Required: {{ recovery_actions.rollback_required }}
      Notification Required: {{ recovery_actions.notification_required }}
      Escalation Required: {{ recovery_actions.escalation_required }}
      Manual Intervention Required: {{ recovery_actions.manual_intervention_required }}
      Retry Possible: {{ recovery_actions.retry_possible }}
      DNS Engineer Notification Required: {{ recovery_actions.dns_engineer_notification_required }}

- name: "Update Error Log"
  ansible.builtin.lineinfile:
    path: "{{ dns_log_config.directory | default('C:\\OE_AAP_LOGS') }}\\{{ dns_log_config.file_name | default('dns_error.log') }}"
    line: |
      {{ ansible_date_time.iso8601 }} [ERROR] PHASE_ERROR: {{ error_classification | to_json }}
    create: true
  delegate_to: "{{ dns_execution_context.primary_server | default('localhost') }}"
  vars:
    ansible_user: "{{ dns_connection_user | default(ansible_user) }}"
    ansible_password: "{{ dns_connection_password | default(ansible_password) }}"
    ansible_connection: "{{ 'winrm' if dns_execution_context.primary_server is defined else 'local' }}"
    ansible_winrm_transport: ntlm
    ansible_winrm_server_cert_validation: ignore
  when:
    - dns_log_config.enabled | default(true) | bool
    - dns_execution_context.primary_server is defined
  ignore_errors: true

- name: "Set Global Error State"
  set_fact:
    global_error_state:
      error_occurred: true
      error_classification: "{{ error_classification }}"
      recovery_actions: "{{ recovery_actions }}"
      rollback_required: "{{ recovery_actions.rollback_required }}"
      phase_failed: "{{ failed_phase }}"
      error_timestamp: "{{ ansible_date_time.iso8601 }}"

- name: "Trigger Rollback if Required"
  set_fact:
    rollback_required: "{{ recovery_actions.rollback_required }}"
  when: recovery_actions.rollback_required | bool

- name: "Prepare Error Notification"
  set_fact:
    error_notification_data:
      subject: "DNS Automation {{ error_classification.severity | upper }} Error - {{ failed_phase | upper }} Phase"
      severity: "{{ error_classification.severity }}"
      phase: "{{ failed_phase }}"
      error_message: "{{ error_message }}"
      execution_id: "{{ execution_context.id | default('unknown') }}"
      job_id: "{{ execution_context.job_id | default('unknown') }}"
      timestamp: "{{ ansible_date_time.iso8601 }}"
      recovery_actions: "{{ recovery_actions }}"

- name: "Phase Error Handler - Complete"
  debug:
    msg: |
      Phase Error Handler - COMPLETED
      ==============================
      Error Processed: {{ error_classification.severity | upper }}
      Rollback Required: {{ recovery_actions.rollback_required }}
      Escalation Required: {{ recovery_actions.escalation_required }}
      Manual Intervention: {{ recovery_actions.manual_intervention_required }}

      Next Actions:
      {{ '- Execute rollback procedures' if recovery_actions.rollback_required else '- No rollback required' }}
      {{ '- Send escalation notification' if recovery_actions.escalation_required else '- Standard notification only' }}
      {{ '- Manual intervention required' if recovery_actions.manual_intervention_required else '- Automated recovery possible' }}
