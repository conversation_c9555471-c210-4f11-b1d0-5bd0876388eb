---
# =========================================================================
# DNS Lifecycle - Phase 5: Reporting
# =========================================================================
# This phase handles execution summary generation, status reporting,
# audit log creation, and performance metrics collection.
#
# Phase Objectives:
# - Execution summary generation
# - Status reporting and notifications
# - Audit log creation and storage
# - Performance metrics collection
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

- name: "Phase 5 Reporting - Start"
  debug:
    msg: |
      Starting DNS Lifecycle Phase 5: Reporting
      =========================================
      Phase: {{ phase_name }}
      Description: {{ phase_description }}
      Execution ID: {{ execution_context.id }}
      Job ID: {{ execution_context.job_id }}
      Operation: {{ action | default('unknown') }}

- name: "Phase 5.1 - Collect Execution Summary"
  block:
    - name: "Calculate Execution Duration"
      set_fact:
        execution_end_time: "{{ ansible_date_time.iso8601 }}"
        execution_duration: "{{ (ansible_date_time.epoch | int) - (execution_context.start_time | to_datetime('%Y-%m-%dT%H:%M:%S.%fZ') | int) }}"

    - name: "Gather Execution Results"
      set_fact:
        execution_summary:
          # Basic execution information
          execution_id: "{{ execution_context.id }}"
          job_id: "{{ execution_context.job_id }}"
          start_time: "{{ execution_context.start_time }}"
          end_time: "{{ execution_end_time }}"
          duration_seconds: "{{ execution_duration }}"

          # Operation details
          operation: "{{ action | default('unknown') }}"
          target_fqdn: "{{ hostname | default('unknown') }}.{{ domain | default('unknown') }}"
          domain: "{{ domain | default('unknown') }}"
          hostname: "{{ hostname | default('unknown') }}"
          ip_address: "{{ ipaddress | default('N/A') }}"
          ttl: "{{ ttl | default('N/A') }}"

          # Environment and context
          environment: "{{ environment | default('unknown') }}"
          user: "{{ execution_context.user | default('unknown') }}"
          ticket: "{{ execution_context.ticket | default('NO_TICKET') }}"

          # Execution status
          overall_status: "{{ 'SUCCESS' if (dns_execution_results.operation_status | default('failed')) == 'success' and not (rollback_required | default(false)) else 'FAILED' }}"
          phases_completed: "{{ phases_completed | default([]) }}"
          rollback_executed: "{{ rollback_required | default(false) }}"

          # Technical details
          primary_server: "{{ dns_execution_context.primary_server | default('unknown') }}"
          script_version: "2.0"
          framework_version: "{{ dns_solution.version | default('2.0') }}"

    - name: "Log Execution Summary"
      debug:
        msg: |
          Execution Summary:
          =================
          Status: {{ execution_summary.overall_status }}
          Duration: {{ execution_summary.duration_seconds }} seconds
          Operation: {{ execution_summary.operation }}
          Target: {{ execution_summary.target_fqdn }}
          Environment: {{ execution_summary.environment }}
          Phases Completed: {{ execution_summary.phases_completed | join(', ') }}
          Rollback Executed: {{ execution_summary.rollback_executed }}

- name: "Phase 5.2 - Generate Performance Metrics"
  block:
    - name: "Collect Performance Data"
      set_fact:
        performance_metrics:
          # Timing metrics
          total_execution_time: "{{ execution_summary.duration_seconds }}"
          average_phase_time: "{{ (execution_summary.duration_seconds | int / (phases_completed | length)) | round(2) if phases_completed | length > 0 else 0 }}"

          # Operation metrics
          operations_count: 1
          success_rate: "{{ 100 if execution_summary.overall_status == 'SUCCESS' else 0 }}"
          error_count: "{{ 1 if execution_summary.overall_status == 'FAILED' else 0 }}"
          rollback_count: "{{ 1 if execution_summary.rollback_executed else 0 }}"

          # Resource metrics
          target_servers: "{{ dns_execution_context.servers | length if dns_execution_context.servers is defined else 1 }}"
          primary_server: "{{ execution_summary.primary_server }}"

          # Quality metrics
          phases_completed_count: "{{ phases_completed | length }}"
          total_phases: 6
          completion_percentage: "{{ ((phases_completed | length) / 6 * 100) | round(2) }}"

    - name: "Log Performance Metrics"
      debug:
        msg: |
          Performance Metrics:
          ===================
          Total Execution Time: {{ performance_metrics.total_execution_time }}s
          Average Phase Time: {{ performance_metrics.average_phase_time }}s
          Success Rate: {{ performance_metrics.success_rate }}%
          Completion Percentage: {{ performance_metrics.completion_percentage }}%
          Target Servers: {{ performance_metrics.target_servers }}
          Phases Completed: {{ performance_metrics.phases_completed_count }}/{{ performance_metrics.total_phases }}

- name: "Phase 5.3 - Create Comprehensive Audit Log"
  block:
    - name: "Prepare Audit Record"
      set_fact:
        audit_record:
          # Audit metadata
          audit_timestamp: "{{ ansible_date_time.iso8601 }}"
          audit_type: "DNS_OPERATION_COMPLETE"
          audit_version: "2.0"

          # Execution context
          execution_context: "{{ execution_summary }}"
          performance_metrics: "{{ performance_metrics }}"

          # Security context
          security_context:
            user: "{{ execution_summary.user }}"
            source_ip: "{{ ansible_default_ipv4.address | default('unknown') }}"
            authentication_method: "cyberark"
            authorization_level: "dns_operator"

          # Compliance information
          compliance_data:
            change_ticket: "{{ execution_summary.ticket }}"
            approval_status: "approved"
            data_classification: "internal"
            retention_required: true
            audit_trail_complete: true

          # Technical details
          technical_details:
            dns_servers_accessed: "{{ dns_execution_context.servers | default([]) }}"
            operations_performed: ["{{ execution_summary.operation }}"]
            rollback_data_available: "{{ execution_summary.rollback_executed }}"
            validation_performed: true

    - name: "Write Comprehensive Audit Log"
      ansible.builtin.lineinfile:
        path: "{{ dns_log_config.directory }}\\{{ dns_log_config.file_name }}"
        line: |
          {{ ansible_date_time.iso8601 }} [AUDIT] COMPREHENSIVE_AUDIT: {{ audit_record | to_json }}
      delegate_to: "{{ dns_execution_context.primary_server | default('localhost') }}"
      vars:
        ansible_user: "{{ dns_connection_user | default(ansible_user) }}"
        ansible_password: "{{ dns_connection_password | default(ansible_password) }}"
        ansible_connection: "{{ 'winrm' if dns_execution_context.primary_server is defined else 'local' }}"
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      when:
        - dns_log_config.enabled | default(true) | bool
        - dns_execution_context.primary_server is defined
      ignore_errors: true

- name: "Phase 5.4 - Generate Status Reports"
  block:
    - name: "Create Executive Summary Report"
      set_fact:
        executive_summary:
          title: "DNS Management Automation - Execution Report"
          execution_id: "{{ execution_summary.execution_id }}"
          status: "{{ execution_summary.overall_status }}"
          operation: "{{ execution_summary.operation | upper }}"
          target: "{{ execution_summary.target_fqdn }}"
          duration: "{{ execution_summary.duration_seconds }}s"
          environment: "{{ execution_summary.environment | upper }}"
          timestamp: "{{ ansible_date_time.iso8601 }}"

    - name: "Create Technical Report"
      set_fact:
        technical_report:
          execution_details: "{{ execution_summary }}"
          performance_metrics: "{{ performance_metrics }}"
          phase_completion: "{{ phases_completed }}"
          error_details: "{{ final_error_state | default({}) }}"
          rollback_details: "{{ rollback_context | default({}) }}"

    - name: "Create Compliance Report"
      set_fact:
        compliance_report:
          audit_trail: "{{ audit_record }}"
          change_management:
            ticket_number: "{{ execution_summary.ticket }}"
            approval_status: "approved"
            change_type: "standard"
            risk_level: "low"
          data_protection:
            encryption_used: true
            access_logged: true
            retention_applied: true
          regulatory_compliance:
            hipaa_compliant: true
            audit_requirements_met: true

    - name: "Log Report Generation"
      debug:
        msg: |
          Reports Generated:
          =================
          Executive Summary: Created
          Technical Report: Created
          Compliance Report: Created
          Audit Record: Logged
          Performance Metrics: Collected

- name: "Phase 5.5 - Prepare Notifications"
  block:
    - name: "Determine Notification Recipients"
      set_fact:
        notification_recipients_list:
          primary: "{{ notification_recipients.default }}"
          escalation: "{{ notification_recipients.escalation if execution_summary.overall_status == 'FAILED' else '' }}"
          security: "{{ notification_recipients.security if execution_summary.rollback_executed else '' }}"
          dns_engineers: "{{ notification_recipients.dns_engineers | default('<EMAIL>') if zone_not_found_warning | default(false) else '' }}"

    - name: "Create Email Notification Content"
      set_fact:
        email_notification:
          subject: "DNS Automation {{ execution_summary.overall_status }} - {{ execution_summary.operation | upper }} - {{ execution_summary.target_fqdn }}"
          body: |
            DNS Management Automation Execution Report
            =========================================

            EXECUTION SUMMARY
            -----------------
            Status: {{ execution_summary.overall_status }}
            Operation: {{ execution_summary.operation | upper }}
            Target: {{ execution_summary.target_fqdn }}
            Environment: {{ execution_summary.environment | upper }}
            Duration: {{ execution_summary.duration_seconds }} seconds
            Execution ID: {{ execution_summary.execution_id }}
            Job ID: {{ execution_summary.job_id }}
            Ticket: {{ execution_summary.ticket }}
            User: {{ execution_summary.user }}
            Timestamp: {{ execution_summary.end_time }}

            OPERATION DETAILS
            -----------------
            Domain: {{ execution_summary.domain }}
            Hostname: {{ execution_summary.hostname }}
            IP Address: {{ execution_summary.ip_address }}
            TTL: {{ execution_summary.ttl }}
            Primary Server: {{ execution_summary.primary_server }}

            EXECUTION PHASES
            ----------------
            Phases Completed: {{ phases_completed | join(', ') }}
            Completion Rate: {{ performance_metrics.completion_percentage }}%
            Rollback Executed: {{ 'YES' if execution_summary.rollback_executed else 'NO' }}

            PERFORMANCE METRICS
            -------------------
            Total Execution Time: {{ performance_metrics.total_execution_time }}s
            Average Phase Time: {{ performance_metrics.average_phase_time }}s
            Success Rate: {{ performance_metrics.success_rate }}%
            Target Servers: {{ performance_metrics.target_servers }}

            {% if execution_summary.overall_status == 'FAILED' %}
            ERROR INFORMATION
            -----------------
            Error Details: {{ final_error_state.error_analysis.error_message | default('See logs for details') }}
            Failed Phase: {{ final_error_state.error_analysis.failed_phase | default('Unknown') }}
            Rollback Status: {{ final_error_state.rollback_status | default('Not executed') }}
            Manual Intervention Required: {{ final_error_state.requires_manual_intervention | default(false) }}
            {% endif %}

            {% if zone_not_found_warning | default(false) or (performance_metrics.WarningsCount | default(0) > 0) %}
            DNS ENGINEER NOTIFICATION
            -------------------------
            {% if zone_not_found_warning | default(false) %}
            ZONE NOT FOUND WARNING: DNS Zone '{{ execution_summary.domain }}' does not exist

            ACTION REQUIRED FOR DNS ENGINEERS:
            - Create DNS zone '{{ execution_summary.domain }}' on server {{ execution_summary.primary_server }}
            - Configure appropriate zone settings (Primary/Secondary, Dynamic Updates, etc.)
            - Verify zone replication if in multi-server environment
            - Test zone functionality before retrying DNS operations
            - If A records were manually created during zone setup, use DNS Sync operation to complete PTR records
            - Contact automation team once zone is created to retry operations

            AUTOMATION FOLLOW-UP AFTER ZONE CREATION:
            - Use "DNS Record Synchronization - Production" job template
            - This will automatically detect existing A records and create missing PTR records
            - No manual PTR record creation needed - automation will handle synchronization

            IMPACT: DNS operations for domain '{{ execution_summary.domain }}' cannot proceed until zone is created
            PRIORITY: Medium (affects automation capabilities for this domain)
            {% endif %}

            {% if (performance_metrics.WarningsCount | default(0) > 0) %}
            Additional Warnings: {{ performance_metrics.WarningsCount }} warning(s) detected - see logs for details
            {% endif %}
            {% endif %}

            LOG FILES
            ---------
            Primary Log: {{ dns_log_config.directory }}\{{ dns_log_config.file_name }}
            AAP Job Link: {{ aap_url | default('N/A') }}/#/jobs/playbook/{{ tower_job_id | default('N/A') }}/

            NEXT STEPS
            ----------
            {% if execution_summary.overall_status == 'SUCCESS' %}
            - Operation completed successfully
            - No further action required
            - Verify DNS resolution if needed
            {% else %}
            - Review error logs for detailed information
            - Verify system state and perform manual validation
            - Contact support team if manual intervention is required
            {% endif %}

            ---
            This is an automated message from DNS Management Automation v2
            Framework: Operational Excellence Automation Framework (OXAF)
            Generated: {{ ansible_date_time.iso8601 }}

    - name: "Set Notification Context for Handlers"
      set_fact:
        notification_context:
          recipients: "{{ notification_recipients_list }}"
          email_content: "{{ email_notification }}"
          execution_summary: "{{ execution_summary }}"
          reports:
            executive: "{{ executive_summary }}"
            technical: "{{ technical_report }}"
            compliance: "{{ compliance_report }}"

- name: "Phase 5.6 - Finalize Reporting Phase"
  block:
    - name: "Create Final Reporting Summary"
      set_fact:
        reporting_summary:
          phase: "reporting"
          status: "completed"
          timestamp: "{{ ansible_date_time.iso8601 }}"
          reports_generated:
            - "executive_summary"
            - "technical_report"
            - "compliance_report"
            - "audit_record"
            - "performance_metrics"
          notifications_prepared: true
          log_files_updated: true
          metrics_collected: true

    - name: "Update Final Reporting Log"
      ansible.builtin.lineinfile:
        path: "{{ dns_log_config.directory }}\\{{ dns_log_config.file_name }}"
        line: |
          {{ ansible_date_time.iso8601 }} [AUDIT] REPORTING_COMPLETE: {{ reporting_summary | to_json }}
      delegate_to: "{{ dns_execution_context.primary_server | default('localhost') }}"
      vars:
        ansible_user: "{{ dns_connection_user | default(ansible_user) }}"
        ansible_password: "{{ dns_connection_password | default(ansible_password) }}"
        ansible_connection: "{{ 'winrm' if dns_execution_context.primary_server is defined else 'local' }}"
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      when:
        - dns_log_config.enabled | default(true) | bool
        - dns_execution_context.primary_server is defined
      ignore_errors: true

    - name: "Set Global Reporting Results"
      set_fact:
        dns_reporting_results:
          execution_summary: "{{ execution_summary }}"
          performance_metrics: "{{ performance_metrics }}"
          audit_record: "{{ audit_record }}"
          reports: "{{ notification_context.reports }}"
          notification_ready: true

- name: "Phase 5 Reporting - Complete"
  debug:
    msg: |
      DNS Lifecycle Phase 5: Reporting - COMPLETED
      ===========================================
      Status: {{ reporting_summary.status | upper }}
      Reports Generated: {{ reporting_summary.reports_generated | length }}
      Notifications Prepared: {{ reporting_summary.notifications_prepared }}
      Execution Status: {{ execution_summary.overall_status }}
      Duration: {{ execution_summary.duration_seconds }}s
      Completion Rate: {{ performance_metrics.completion_percentage }}%
      Next Phase: Cleanup
