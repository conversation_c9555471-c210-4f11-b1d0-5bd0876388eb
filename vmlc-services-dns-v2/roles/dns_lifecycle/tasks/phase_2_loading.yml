---
# =========================================================================
# DNS Lifecycle - Phase 2: Loading
# =========================================================================
# This phase handles inventory discovery, credential retrieval, and
# pre-execution environment preparation for DNS management operations.
#
# Phase Objectives:
# - Dynamic inventory population
# - Credential retrieval and validation
# - Configuration loading and validation tasks
# - Pre-execution checks and validations
#
# Author: CES Operational Excellence Team
# Contributor: Muhammad <PERSON> (7409)
# Framework: Operational Excellence Automation Framework (OXAF)
# Version: 2.0
# =========================================================================

- name: "Phase 2 Loading - Start"
  debug:
    msg: |
      Starting DNS Lifecycle Phase 2: Loading
      =======================================
      Phase: {{ phase_name }}
      Description: {{ phase_description }}
      Primary Servers: {{ dns_primary_servers | join(', ') }}
      Backup Servers: {{ dns_backup_servers | join(', ') if dns_backup_servers | length > 0 else 'None' }}

- name: "Phase 2.1 - Credential Retrieval from CyberArk"
  block:
    - name: "Retrieve DNS Service Account Credentials from CyberArk"
      cloud_cpe.cyberark_ccp.cyberark_credential:
        account: "DNS_SERVICE_ACCOUNT"
      register: dns_credentials_result
      no_log: true

    - name: "Validate Retrieved DNS Credentials"
      assert:
        that:
          - dns_credentials_result is succeeded
          - dns_credentials_result.credential is defined
          - dns_credentials_result.credential.username is defined
          - dns_credentials_result.credential.password is defined
        fail_msg: "Failed to retrieve valid DNS credentials from CyberArk"
        success_msg: "DNS credentials successfully retrieved from CyberArk"
      no_log: true

    - name: "Set DNS Connection Variables"
      set_fact:
        dns_connection_user: "{{ dns_credentials_result.credential.username }}"
        dns_connection_password: "{{ dns_credentials_result.credential.password }}"
      no_log: true

    - name: "Log DNS Credential Retrieval Success"
      debug:
        msg: |
          DNS Credential Retrieval Status:
          ===============================
          Status: SUCCESS
          Username: {{ dns_connection_user | regex_replace('(.{3}).*(.{3})', '\\1***\\2') }}
          Source: CyberArk Collection
          Account: DNS_SERVICE_ACCOUNT

  rescue:
    - name: "Credential Retrieval Failed"
      fail:
        msg: "Phase 2 Loading failed during credential retrieval: {{ ansible_failed_result.msg }}"

- name: "Phase 2.2 - DNS Server Discovery and Validation"
  block:
    - name: "Test Connectivity to Primary DNS Servers"
      ansible.builtin.wait_for:
        host: "{{ item }}"
        port: 5985  # WinRM HTTP port
        timeout: "{{ dns_performance_config.connection_timeout }}"
        state: started
      loop: "{{ dns_primary_servers }}"
      register: primary_server_connectivity

    - name: "Test Connectivity to Backup DNS Servers"
      ansible.builtin.wait_for:
        host: "{{ item }}"
        port: 5985  # WinRM HTTP port
        timeout: "{{ dns_performance_config.connection_timeout }}"
        state: started
      loop: "{{ dns_backup_servers }}"
      register: backup_server_connectivity
      when: dns_backup_servers | length > 0
      ignore_errors: true

    - name: "Validate Primary Server Connectivity"
      assert:
        that:
          - primary_server_connectivity.results | selectattr('failed', 'undefined') | list | length > 0
        fail_msg: "No primary DNS servers are reachable"
        success_msg: "At least one primary DNS server is reachable"

    - name: "Set Available DNS Servers"
      set_fact:
        dns_available_servers: "{{ primary_server_connectivity.results | selectattr('failed', 'undefined') | map(attribute='item') | list }}"

    - name: "Log Server Connectivity Status"
      debug:
        msg: |
          DNS Server Connectivity Status:
          ===============================
          Available Primary Servers: {{ dns_available_servers | join(', ') }}
          Total Available: {{ dns_available_servers | length }}
          Backup Servers Status: {{ 'Tested' if dns_backup_servers | length > 0 else 'None configured' }}

  rescue:
    - name: "DNS Server Discovery Failed"
      fail:
        msg: "Phase 2 Loading failed during DNS server discovery: {{ ansible_failed_result.msg }}"

- name: "Phase 2.3 - Authentication Validation"
  block:
    - name: "Test Authentication on Primary DNS Server"
      ansible.windows.win_ping:
      delegate_to: "{{ dns_available_servers[0] }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: auth_test_result

    - name: "Validate Authentication Success"
      assert:
        that:
          - auth_test_result is succeeded
        fail_msg: "Authentication failed on primary DNS server"
        success_msg: "Authentication successful on primary DNS server"

    - name: "Test PowerShell Execution Capability"
      ansible.windows.win_shell: |
        $PSVersionTable.PSVersion.Major
      delegate_to: "{{ dns_available_servers[0] }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: powershell_test_result

    - name: "Validate PowerShell Version"
      assert:
        that:
          - powershell_test_result.stdout | int >= 5
        fail_msg: "PowerShell version 5.0 or higher required"
        success_msg: "PowerShell version validation passed"

    - name: "Log Authentication Validation"
      debug:
        msg: |
          Authentication Validation Status:
          ================================
          Status: SUCCESS
          Server: {{ dns_available_servers[0] }}
          PowerShell Version: {{ powershell_test_result.stdout | trim }}
          Connection Method: WinRM/NTLM

  rescue:
    - name: "Authentication Validation Failed"
      fail:
        msg: "Phase 2 Loading failed during authentication validation: {{ ansible_failed_result.msg }}"

- name: "Phase 2.4 - DNS Service Validation"
  block:
    - name: "Check DNS Server Service Status"
      ansible.windows.win_service:
        name: DNS
      delegate_to: "{{ dns_available_servers[0] }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: dns_service_status

    - name: "Validate DNS Service is Running"
      assert:
        that:
          - dns_service_status.state == 'running'
        fail_msg: "DNS service is not running on {{ dns_available_servers[0] }}"
        success_msg: "DNS service is running on {{ dns_available_servers[0] }}"

    - name: "Test DNS Zone Access"
      ansible.windows.win_shell: |
        try {
            $zone = Get-DnsServerZone -Name "{{ domain }}" -ErrorAction Stop
            Write-Output "Zone found: $($zone.ZoneName)"
            Write-Output "Zone type: $($zone.ZoneType)"
            Write-Output "Dynamic update: $($zone.DynamicUpdate)"
        } catch {
            if ($_.Exception.Message -like "*zone*not*found*" -or $_.Exception.Message -like "*Zone*not*found*") {
                Write-Warning "Zone not found: {{ domain }} - DNS engineers need to create zone manually"
                Write-Output "ZONE_NOT_FOUND_WARNING: {{ domain }}"
                Write-Output "DNS engineers need to create zone '{{ domain }}' manually before DNS operations can proceed"
                # Don't exit with error for zone not found - this is a warning condition
                exit 0
            } else {
                Write-Error "Zone access failed: $($_.Exception.Message)"
                exit 1
            }
        }
      delegate_to: "{{ dns_available_servers[0] }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: dns_zone_test

    - name: "Check for Zone Not Found Warning"
      set_fact:
        zone_not_found_warning: "{{ 'ZONE_NOT_FOUND_WARNING' in dns_zone_test.stdout }}"
      when: dns_zone_test is defined

    - name: "Log DNS Service Validation"
      debug:
        msg: |
          DNS Service Validation Status:
          =============================
          Service Status: {{ dns_service_status.state }}
          Zone Access: {{ 'WARNING - Zone Not Found' if zone_not_found_warning | default(false) else 'SUCCESS' }}
          Zone Details: {{ dns_zone_test.stdout_lines | join(' | ') }}
          {% if zone_not_found_warning | default(false) %}
          Zone Warning: DNS engineers need to create zone '{{ domain }}' manually
          {% endif %}

  rescue:
    - name: "DNS Service Validation Failed"
      fail:
        msg: "Phase 2 Loading failed during DNS service validation: {{ ansible_failed_result.msg }}"

- name: "Phase 2.5 - Script Deployment Preparation"
  block:
    - name: "Create Script Directory"
      ansible.windows.win_file:
        path: "C:\\ansscripts"
        state: directory
      delegate_to: "{{ item }}"
      loop: "{{ dns_available_servers }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore

    - name: "Check Existing Script Files"
      ansible.windows.win_stat:
        path: "C:\\ansscripts\\set-dns-v2.ps1"
      delegate_to: "{{ dns_available_servers[0] }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: existing_script_check

    - name: "Backup Existing Script if Present"
      ansible.windows.win_copy:
        src: "C:\\ansscripts\\set-dns-v2.ps1"
        dest: "C:\\ansscripts\\set-dns-v2.ps1.backup.{{ ansible_date_time.epoch }}"
        remote_src: true
      delegate_to: "{{ dns_available_servers[0] }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      when: existing_script_check.stat.exists

    - name: "Log Script Deployment Preparation"
      debug:
        msg: |
          Script Deployment Preparation:
          =============================
          Script Directory: C:\ansscripts
          Existing Script: {{ 'Found and backed up' if existing_script_check.stat.exists else 'Not found' }}
          Target Servers: {{ dns_available_servers | join(', ') }}

- name: "Phase 2.6 - Pre-execution Validation"
  block:
    - name: "Validate Operation Prerequisites"
      assert:
        that:
          - dns_available_servers | length > 0
          - dns_connection_user is defined
          - dns_connection_password is defined
          - domain is defined
          - hostname is defined
          - action in dns_operations.supported_actions
        fail_msg: "Pre-execution validation failed - missing prerequisites"
        success_msg: "Pre-execution validation passed"

    - name: "Set Execution Context"
      set_fact:
        dns_execution_context:
          servers: "{{ dns_available_servers }}"
          primary_server: "{{ dns_available_servers[0] }}"
          domain: "{{ domain }}"
          hostname: "{{ hostname }}"
          action: "{{ dns_action }}"
          ip_address: "{{ ipaddress | default('') }}"
          ttl: "{{ ttl | default(dns_operations.default_settings.ttl) }}"
          user: "{{ dns_connection_user }}"
          environment: "{{ dns_environment }}"
          security_zone: "{{ dns_security_zone }}"

    - name: "Log Pre-execution Validation"
      debug:
        msg: |
          Pre-execution Validation Status:
          ===============================
          Status: SUCCESS
          Available Servers: {{ dns_execution_context.servers | length }}
          Primary Server: {{ dns_execution_context.primary_server }}
          Operation: {{ dns_execution_context.action }}
          Target: {{ dns_execution_context.hostname }}.{{ dns_execution_context.domain }}
          Environment: {{ dns_execution_context.environment }}
          Security Zone: {{ dns_execution_context.security_zone }}

- name: "Phase 2 Loading - Complete"
  debug:
    msg: |
      DNS Lifecycle Phase 2: Loading - COMPLETED
      =========================================
      Status: SUCCESS
      Credentials: Retrieved and validated
      DNS Servers: {{ dns_available_servers | length }} available
      Primary Server: {{ dns_execution_context.primary_server }}
      DNS Service: Validated and running
      Zone Access: Confirmed
      Next Phase: Execution
