# The IACOPS-Method (Infrastructure as Code Operations Framework)

Current Version: V4 Release - "IaC DevOps Agent Framework"
Author: CES Operational Excellence Team, Contributor: <PERSON> (7409)

A comprehensive AI-driven framework for Infrastructure as Code (IaC) DevOps operations in on-premise environments, supporting Windows Servers, RHEL, Oracle Linux, VMware ESXi, Nutanix HCI, Red Hat OpenShift, and Ansible Automation Platform.

## Infrastructure DevOps Quickstart

The IaCOps Framework provides specialized AI agents for infrastructure operations, designed specifically for on-premise environments with enterprise-grade infrastructure platforms.

**Supported Infrastructure Stack:**
- **Compute Platforms:** Windows Servers, Red Hat Enterprise Linux, Oracle Linux
- **Virtualization:** VMware ESXi, Nutanix HCI
- **Container Platform:** Red Hat OpenShift
- **Automation Platform:** Ansible Automation Platform (AAP)
- **Project Management:** Jira, ServiceNow
- **Version Control:** Bitbucket

**Key Features:**
- Six-phase infrastructure lifecycle management (Configuration, Loading, Execution, Error Handling, Reporting, Cleanup)
- Comprehensive logging and audit trails
- Quality gates and compliance validation
- Idempotent operations with rollback capabilities

## IDE Project Quickstart

Starting with the IaCOps Framework is straightforward - copy the `bmad-agent` folder to your infrastructure project. The framework includes specialized infrastructure agents optimized for Augment Code VS Code IDE integration.

**Quick Setup:**
1. Copy `bmad-agent` folder to your project root
2. Configure the [IDE orchestrator](./bmad-agent/ide-iacops-orchestrator.md)
3. Select infrastructure personas from [configuration](./bmad-agent/ide-iacops-orchestrator-cfg.md)

**Infrastructure Artifacts Location:**
- Infrastructure designs: `(project-root)/docs/infrastructure/`
- Ansible playbooks: `(project-root)/ansible/`
- Deployment pipelines: `(project-root)/pipelines/`
- Monitoring configs: `(project-root)/monitoring/`
- Documentation: `(project-root)/docs/`

## Advancing AI-Driven Development

Welcome to the latest and most advanced yet easy to use version of the Web and IDE Agent Agile Workflow! This new version, called BMad Agent, represents a significant evolution that builds but vastly improves upon the foundations of [legacy V2](./legacy-archive/V2/), introducing a more refined and comprehensive suite of agents, templates, checklists, tasks - and the amazing BMad Orchestrator and Knowledge Base agent is now available - a master of every aspect of the method that can become any agent and even handle multiple tasks all within a single massive web context if so desired.

## What's New?

All IDE Agents are now optimized to be under 6K characters, so they will work with windsurf's file limit restrictions.

The method now has an uber Orchestrator called BMAD - this agent will take your web or ide usage to the next level - this agent can morph and become the specific agent you want to work with! This makes Web usage super easy to use and set up. And in the IDE - you do not have to set up so many different agents if you do not want to!

There have been drastic improvements to the generation of documents and artifacts and the agents are now programmed to really help you build the best possible plans. Advanced LLM prompting techniques have been incorporated and programmed to help you help the agents produce amazing accurate artifacts, unlike anything seen before. Additionally agents are now configurable in what they can and cannot do - so you can accept the defaults, or set which personas are able to do what tasks. If you think the PO should be the one generating PRDs and the Scrum Master should be your course corrector - its all possible now! **Define agile the BMad way - or your way!**

While this is very powerful - you can get started with the default recommended set up as is in this repo, and basically use the agents as they are envisioned and will be explained. Detailed configuration and usage is outlined in the [Instructions](./docs/instruction.md)

## What is the BMad Method?

The BMad Method is a revolutionary approach that elevates "vibe coding" to advanced project planning to ensure your developer agents can start and completed advanced projects with very explicit guidance. It provides a structured yet flexible framework to plan, execute, and manage software projects using a team of specialized AI agents.

This method and tooling is so much more than just a task runner - this is a refined tool that will help you bring out your best ideas, define what you really are to build, and execute on it! From ideation, to PRD creation, to the technical decision making - this will help you do it all with the power of advanced LLM guidance.

The method is designed to be tool-agnostic in principle, with agent instructions and workflows adaptable to various AI platforms and IDEs.

## Agile Agents

Agents are programmed either directly self contained to drop right into an agent config in the ide - or they can be configured as programmable entities the orchestrating agent can become.

### Web Agents

Gemini 2.5 or Open AI customGPTs are created by running the node build script to generate output to a build folder. This output is the full package to create the orchestrator web agent.

See the detailed [Web Orchestration Setup and Usage Instructions](./docs/instruction.md#setting-up-web-agent-orchestrator)

### IDE Agents

There are dedicated self contained agents that are stand alone, and also an IDE version of an orchestrator. For there standalone, there are:

- [Dev IDE Agent](./bmad-agent/personas/dev.ide.md)
- [Story Generating SM Agent](./bmad-agent/personas/sm.ide.md)

If you want to use the other agents, you can use the other agents from that folder - but some will be larger than Windsurf allows - and there are many agents. So its recommended to either use 1 off tasks - OR even better - use the IDE Orchestrator Agent. See these [set up and Usage instructions for IDE Orchestrator](./docs/instruction.md#ide-agent-setup-and-usage).

## Tasks

Located in `bmad-agent/tasks/`, these self-contained instruction sets allow IDE agents or the orchestrators configured agents to perform specific jobs. These also can be used as one off commands with a vanilla agent in the ide by just referencing the task and asking the agent to perform it.

**Purpose:**

- **Reduce Agent Bloat:** Avoid adding rarely used instructions to primary agents.
- **On-Demand Functionality:** Instruct any capable IDE agent to execute a task by providing the task file content.
- **Versatility:** Handles specific functions like running checklists, creating stories, sharding documents, indexing libraries, etc.

Think of tasks as specialized mini-agents callable by your main IDE agents.

## End Matter

Interested in improving the BMAD Method? See the [contributing guidelines](docs/CONTRIBUTING.md).

Thank you and enjoy - BMad!
[License](./docs/LICENSE)
