# Configuration Guide - DNS Management Automation v2

## Comprehensive Configuration Reference

**Framework:** Operational Excellence Automation Framework (OXAF)  
**Version:** 2.0  
**Purpose:** Complete configuration guide for DNS Management Automation v2  

---

## 🎯 **Overview**

This guide provides comprehensive configuration instructions for the DNS Management Automation v2 solution. It covers all configuration files, variables, and settings required for proper deployment and operation across different environments.

### **Configuration Components:**
- ✅ **Environment Variables** - Production, Staging, Development settings
- ✅ **DNS Server Configuration** - DNS server mappings and credentials
- ✅ **JIRA Integration** - Service request integration settings
- ✅ **CyberArk Integration** - Secure credential management
- ✅ **Monitoring Integration** - Splunk, Prometheus, DataDog settings
- ✅ **Security Configuration** - Compliance and audit settings

---

## 📁 **Configuration File Structure**

### **Main Configuration Files:**
```
vmlc-services-dns-v2/
├── group_vars/
│   ├── all/
│   │   ├── main.yml                 # Global configuration
│   │   └── jira_config.yml          # JIRA integration settings
│   ├── production/
│   │   └── main.yml                 # Production environment
│   ├── staging/
│   │   └── main.yml                 # Staging environment
│   └── development/
│       └── main.yml                 # Development environment
├── inventory/
│   └── production/
│       └── hosts.yml                # Inventory configuration
└── collections/
    └── requirements.yml             # Collection dependencies
```

---

## 🌍 **Environment Configuration**

### **Production Environment (`group_vars/production/main.yml`):**

#### **DNS Server Configuration:**
```yaml
# Production DNS servers
dns_servers:
  primary: "dns-prod-01.healthgrp.com.sg"
  secondary: "dns-prod-02.healthgrp.com.sg"
  
# Domain mappings
domain_server_mapping:
  "healthgrp.com.sg":
    primary: "dns-prod-01.healthgrp.com.sg"
    environment: "production"
    security_zone: "internal"
```

#### **JIRA Configuration:**
```yaml
# Production JIRA credentials (CyberArk-based)
jira_credentials:
  username_account: "JIRA_PROD_USERNAME"
  password_account: "JIRA_PROD_PASSWORD"
  grid_token_account: "JIRA_PROD_GRID_TOKEN"
  api_base_url: "https://itsm.hcloud.healthgrp.com.sg/rest/api/2"
  grid_base_url: "https://itsm.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid"
```

#### **Monitoring Configuration:**
```yaml
# Production monitoring
monitoring_integration:
  enabled: true
  platforms:
    - "splunk"
    - "prometheus"
    - "datadog"
  metrics_endpoint: "{{ metrics_endpoint_prod }}"
  alerting_enabled: true
  dashboard_enabled: true
```

### **Staging Environment (`group_vars/staging/main.yml`):**

#### **DNS Server Configuration:**
```yaml
# Staging DNS servers
dns_servers:
  primary: "dns-stg-01.stghealthgrp.com.sg"
  secondary: "dns-stg-02.stghealthgrp.com.sg"
  
# Domain mappings
domain_server_mapping:
  "stghealthgrp.com.sg":
    primary: "dns-stg-01.stghealthgrp.com.sg"
    environment: "staging"
    security_zone: "internal"
```

#### **JIRA Configuration:**
```yaml
# Staging JIRA credentials (uses UAT instance)
jira_credentials:
  username_account: "JIRA_UAT_USERNAME"
  password_account: "JIRA_UAT_PASSWORD"
  grid_token_account: "JIRA_UAT_GRID_TOKEN"
  api_base_url: "https://jsd-uat.hcloud.healthgrp.com.sg/rest/api/2"
  grid_base_url: "https://jsd-uat.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid"
```

### **Development Environment (`group_vars/development/main.yml`):**

#### **DNS Server Configuration:**
```yaml
# Development DNS servers
dns_servers:
  primary: "dns-dev-01.devhealthgrp.com.sg"
  secondary: "dns-dev-02.devhealthgrp.com.sg"
  
# Domain mappings
domain_server_mapping:
  "devhealthgrp.com.sg":
    primary: "dns-dev-01.devhealthgrp.com.sg"
    environment: "development"
    security_zone: "internal"
```

---

## 🔧 **Global Configuration (`group_vars/all/main.yml`)**

### **DNS Solution Configuration:**
```yaml
# DNS solution metadata
dns_solution:
  name: "DNS Management Automation"
  version: "2.0"
  framework: "Operational Excellence Automation Framework (OXAF)"
  author: "CES Operational Excellence Team"
  contributor: "Muhammad Syazani Bin Mohamed Khairi (7409)"
```

### **Operation Configuration:**
```yaml
# DNS operation settings
dns_operations:
  default_ttl: 3600
  max_retry_attempts: 3
  retry_delay_seconds: 5
  timeout_seconds: 300
  
  # Supported operations
  supported_actions:
    - "verify"
    - "add"
    - "remove"
    - "update"
    - "sync"
```

### **Security Configuration:**
```yaml
# Security settings
security_configuration:
  encryption_enabled: true
  audit_logging: true
  credential_rotation: true
  access_control: "rbac"
  
# CyberArk configuration (collection-based)
cyberark_configuration:
  enabled: true
  # Collection handles all configuration automatically
```

### **Compliance Configuration:**
```yaml
# Compliance frameworks
compliance_configuration:
  frameworks:
    hipaa: true
    pci_dss: true
    sox: true
    iso27001: true
    
  # Audit requirements
  audit_requirements:
    change_tracking: true
    approval_workflows: true
    segregation_of_duties: true
    data_retention: true
    real_time_monitoring: true
```

---

## 🎫 **JIRA Integration Configuration (`group_vars/all/jira_config.yml`)**

### **JIRA Validation Rules:**
```yaml
# Ticket validation
jira_validation_rules:
  ticket_number:
    pattern: "^(SR|SCR|INC)-\\d+$"
    required: true
    error_message: "Invalid JIRA ticket number format. Expected: SR-XXXXXX, SCR-XXXXXX, or INC-XXXXXX"
    supported_types:
      - "SR"   # Service Requests
      - "SCR"  # Service Change Requests  
      - "INC"  # Incidents
```

### **JIRA API Configuration:**
```yaml
# JIRA API settings
jira_api_configuration:
  timeout_seconds: 30
  retry_attempts: 3
  retry_delay_seconds: 5
  validate_ssl: false
  
  # API versions
  api_version: "2"
  grid_api_version: "1.0"
  
  # Update methods
  update_method: "grid_api"  # grid_api or rest_api
  update_field: "remark"
  
  # Error handling
  ignore_errors: true
  log_failures: true
  notify_on_failure: true
```

### **JIRA Environment Mapping:**
```yaml
# Environment-specific JIRA instances
jira_environment_mapping:
  production:
    instance: "itsm"
    base_url: "https://itsm.hcloud.healthgrp.com.sg"
    description: "Production ITSM"
    
  staging:
    instance: "jsd-uat"
    base_url: "https://jsd-uat.hcloud.healthgrp.com.sg"
    description: "UAT JIRA Service Desk"
    
  development:
    instance: "jsd-uat"
    base_url: "https://jsd-uat.hcloud.healthgrp.com.sg"
    description: "UAT JIRA Service Desk (Dev)"
```

---

## 📊 **Monitoring Configuration**

### **Splunk Integration:**
```yaml
# Splunk configuration
splunk_integration:
  enabled: true
  hec_url: "{{ splunk_hec_url }}"
  hec_token: "{{ splunk_hec_token_from_cyberark }}"
  index: "dns_automation"
  sourcetype: "dns:operation"
```

### **Prometheus Integration:**
```yaml
# Prometheus configuration
prometheus_integration:
  enabled: true
  pushgateway_url: "{{ prometheus_pushgateway_url }}"
  job_name: "dns-automation"
  metrics_prefix: "dns_"
```

### **DataDog Integration:**
```yaml
# DataDog configuration
datadog_integration:
  enabled: true
  api_url: "https://api.datadoghq.com/api/v1"
  api_key: "{{ datadog_api_key_from_cyberark }}"
  tags:
    - "service:dns-automation"
    - "framework:oxaf"
```

---

## 🔒 **Security and Compliance Configuration**

### **Encryption Settings:**
```yaml
# Encryption configuration
encryption_configuration:
  data_at_rest: true
  data_in_transit: true
  key_management: "cyberark"
  algorithm: "AES-256"
```

### **Audit Configuration:**
```yaml
# Audit logging
audit_configuration:
  enabled: true
  log_level: "INFO"
  retention_days: 2555  # 7 years
  real_time_monitoring: true
  
  # Audit events
  events_to_log:
    - "dns_operations"
    - "credential_access"
    - "configuration_changes"
    - "user_actions"
    - "system_events"
```

### **Access Control:**
```yaml
# Role-based access control
rbac_configuration:
  enabled: true
  default_role: "dns_operator"
  
  roles:
    dns_admin:
      permissions:
        - "dns:*"
        - "config:*"
        - "audit:read"
        
    dns_operator:
      permissions:
        - "dns:add"
        - "dns:remove"
        - "dns:update"
        - "dns:verify"
        
    dns_viewer:
      permissions:
        - "dns:verify"
        - "audit:read"
```

---

## 📋 **Inventory Configuration (`inventory/production/hosts.yml`)**

### **DNS Server Inventory:**
```yaml
# Production DNS servers
all:
  children:
    dns_servers:
      hosts:
        dns-prod-01.healthgrp.com.sg:
          ansible_host: "*********"
          dns_role: "primary"
          environment: "production"
          
        dns-prod-02.healthgrp.com.sg:
          ansible_host: "*********"
          dns_role: "secondary"
          environment: "production"
          
    aap_controllers:
      hosts:
        aap-prod-01.healthgrp.com.sg:
          ansible_host: "*********"
          role: "controller"
          
  vars:
    ansible_user: "{{ dns_service_account }}"
    ansible_password: "{{ dns_service_password }}"
    ansible_connection: "winrm"
    ansible_winrm_transport: "kerberos"
    ansible_winrm_server_cert_validation: "ignore"
```

---

## 🔧 **Collection Configuration (`collections/requirements.yml`)**

### **Required Collections:**
```yaml
# Ansible collections
collections:
  # CyberArk Credential Provider Collection
  - name: cloud_cpe.cyberark_ccp
    version: ">=1.0.0"
    
  # Core Ansible Collections
  - name: ansible.windows
    version: ">=1.0.0"
    
  - name: community.general
    version: ">=1.0.0"
    
  - name: ansible.posix
    version: ">=1.0.0"
    
  # Additional collections
  - name: community.crypto
    version: ">=1.0.0"
    
  - name: ansible.utils
    version: ">=1.0.0"
```

---

## ⚙️ **Configuration Validation**

### **Validation Commands:**
```bash
# Validate configuration syntax
ansible-playbook main.yml --syntax-check

# Validate inventory
ansible-inventory --list

# Test connectivity
ansible all -m ping

# Validate collections
ansible-galaxy collection list
```

### **Configuration Testing:**
```bash
# Test DNS configuration
ansible-playbook main.yml -e "action=verify domain=test.com hostname=test var_sr_number=SR-TEST-001" --check

# Test JIRA integration
ansible-playbook main.yml -e "action=verify domain=test.com hostname=test var_sr_number=SR-123456" --tags="validation,jira"

# Test CyberArk integration
ansible-playbook main.yml --tags="validation,cyberark"
```

---

## 📞 **Configuration Support**

### **Configuration Files Checklist:**
- ✅ **Global Configuration** - `group_vars/all/main.yml`
- ✅ **JIRA Configuration** - `group_vars/all/jira_config.yml`
- ✅ **Environment Configs** - Production, Staging, Development
- ✅ **Inventory Configuration** - DNS servers and AAP controllers
- ✅ **Collection Requirements** - All required collections listed

### **Support Contacts:**
- **Configuration Support:** <EMAIL>
- **Environment Setup:** <EMAIL>
- **Security Configuration:** <EMAIL>
- **JIRA Integration:** <EMAIL>

---

*This configuration guide is part of the DNS Management Automation v2 solution developed by the CES Operational Excellence Team following Operational Excellence Automation Framework (OXAF) patterns.*
