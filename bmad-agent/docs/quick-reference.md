# IaCOps Framework Quick Reference Guide

**Author:** CES Operational Excellence Team, Contributor: <PERSON> (7409)

## Quick Start Commands

### Initialize Framework in Augment Code
```
Load the IaCOps orchestrator from bmad-agent/ide-bmad-orchestrator.md and use the configuration from bmad-agent/ide-bmad-orchestrator-cfg.md
```

### Switch Personas
```
override safety protocol - switch to [Persona Name]
```

### List Available Personas
```
List available infrastructure personas and their capabilities
```

## Infrastructure Personas Quick Reference

| Persona | Name | Specialization | Key Tasks |
|---------|------|----------------|-----------|
| **Infrastructure Architect** | Alex | Strategic design, capacity planning | Design Infrastructure Architecture, Create Capacity Planning, Design Backup Recovery |
| **Platform Engineer** | Jordan | OpenShift, containers, CI/CD | Design Deployment Pipeline, Create OpenShift Configuration, Design Container Strategy |
| **Systems Engineer** | Sam | Windows/Linux servers, virtualization | Configure Server Infrastructure, Design Virtualization Strategy, Create System Hardening |
| **Automation Engineer** | Casey | Ansible, AAP workflows | Create Ansible Playbook, Design Automation Workflow, Create AAP Job Template |
| **DevOps Engineer** | Taylor | End-to-end pipelines, monitoring | Create Monitoring Strategy, Design Change Management, Create Incident Response |
| **Security Engineer** | Morgan | Security compliance, hardening | Create Security Hardening, Design Compliance Audit, Create Security Policy |
| **Operations Engineer** | Riley | Incident response, troubleshooting | Create Incident Response, Design Maintenance Procedure, Create Troubleshooting Guide |

## Technology Stack Reference

### Approved Infrastructure Platforms
- **Compute:** Windows Servers, Red Hat Enterprise Linux, Oracle Linux
- **Virtualization:** VMware ESXi, Nutanix HCI
- **Container Platform:** Red Hat OpenShift
- **Automation:** Ansible Automation Platform (AAP)
- **Project Management:** Jira, ServiceNow
- **Version Control:** Bitbucket

### Six-Phase Lifecycle Framework
1. **Configuration** - Environment setup and variable loading
2. **Loading** - Dynamic inventory and credential retrieval
3. **Execution** - Core automation task execution
4. **Error Handling** - Error detection and rollback procedures
5. **Reporting** - Status reporting and audit logging
6. **Cleanup** - Resource cleanup and state management

## Common Task Workflows

### Infrastructure Design Workflow
```
1. Infrastructure Architect → Design Infrastructure Architecture
2. Platform Engineer → Create OpenShift Configuration (if applicable)
3. Systems Engineer → Configure Server Infrastructure
4. Security Engineer → Create Security Hardening
5. DevOps Engineer → Create Monitoring Strategy
```

### Automation Development Workflow
```
1. Automation Engineer → Create Ansible Playbook
2. Security Engineer → Create Security Policy
3. DevOps Engineer → Design Change Management
4. Operations Engineer → Create Troubleshooting Guide
```

### Deployment Pipeline Workflow
```
1. Platform Engineer → Design Deployment Pipeline
2. Automation Engineer → Create AAP Job Template
3. DevOps Engineer → Create Monitoring Strategy
4. Operations Engineer → Create Incident Response
```

## File Structure Reference

```
project-root/
├── bmad-agent/                    # IaCOps Framework
│   ├── personas/                  # AI agent personas
│   │   ├── infrastructure-architect.md
│   │   ├── automation-engineer.md
│   │   └── [other personas]
│   ├── tasks/                     # Structured task workflows
│   │   ├── design-infrastructure-architecture.md
│   │   ├── create-ansible-playbook.md
│   │   └── [other tasks]
│   ├── templates/                 # Document templates
│   │   ├── infrastructure-architecture-tmpl.md
│   │   ├── ansible-playbook-tmpl.md
│   │   └── [other templates]
│   ├── checklists/               # Quality validation checklists
│   │   ├── infrastructure-design-checklist.md
│   │   └── [other checklists]
│   ├── data/                     # Knowledge base and configuration
│   │   ├── bmad-kb.md
│   │   └── [other data files]
│   └── docs/                     # Framework documentation
│       ├── augment-code-usage-guide.md
│       └── quick-reference.md
├── docs/                         # Project documentation
│   ├── infrastructure/           # Infrastructure designs
│   ├── security/                # Security policies
│   └── operations/              # Operational procedures
├── ansible/                      # Ansible automation
│   ├── playbooks/               # Ansible playbooks
│   ├── roles/                   # Ansible roles
│   └── inventory/               # Inventory files
├── pipelines/                    # CI/CD pipelines
└── monitoring/                   # Monitoring configurations
```

## Logging Standards

### Log File Naming Convention
```
<AAP JOB ID>_<DDMMYYYY>_<TICKET>_<OPERATION>_<TYPE>.log

Examples:
- 12345_15012024_INC001234_DNS_ANSIBLE.log
- 67890_15012024_CHG005678_ADOB_POWERSHELL.log
```

### Log Storage Location
```
C:\OE_AAP_LOGS\
```

## Quality Gates Checklist

### Before Implementation
- [ ] Requirements documented and validated
- [ ] Technology stack alignment confirmed
- [ ] Security requirements addressed
- [ ] Capacity planning completed
- [ ] Stakeholder approval obtained

### During Implementation
- [ ] Six-phase lifecycle followed
- [ ] Idempotent operations ensured
- [ ] Comprehensive logging implemented
- [ ] Error handling and rollback tested
- [ ] Quality checklist validated

### After Implementation
- [ ] Documentation updated and current
- [ ] Testing and validation completed
- [ ] Monitoring and alerting configured
- [ ] Operational procedures documented
- [ ] Knowledge transfer completed

## Common Commands and Patterns

### Augment Code Integration
```
# Search existing infrastructure code
@codebase-retrieval Show me existing Ansible roles for [technology]

# Reference previous work
Use the [document/configuration] that [persona] created earlier

# Leverage context
Based on the existing infrastructure in [location], create [new component]
```

### Persona Interaction Patterns
```
# Request specific task
[Persona Name] - [Task Name]

# Ask for recommendations
What would you recommend for [specific scenario]?

# Request validation
Validate this [configuration/design] against [requirements/standards]

# Request documentation
Create comprehensive documentation for [component/process]
```

### Template Usage
```
# Use specific template
Use the [template-name] template for this [document/configuration]

# Customize template
Modify the [template-name] template to include [specific requirements]

# Validate against template
Ensure this [document] follows the [template-name] template structure
```

## Troubleshooting Quick Fixes

### Framework Not Loading
1. Verify bmad-agent folder is in project root
2. Check Augment Code extension is active
3. Restart VS Code if necessary

### Persona Not Available
1. Check ide-bmad-orchestrator-cfg.md configuration
2. Verify persona files exist in personas/ directory
3. Ensure file permissions are correct

### Task Execution Issues
1. Verify task files exist in tasks/ directory
2. Check required templates and checklists are available
3. Validate file syntax and formatting

### Context Issues
1. Let Augment Code re-index the project
2. Check file paths and references
3. Ensure consistent naming conventions

---

This quick reference provides immediate access to the most commonly used commands, workflows, and troubleshooting steps for the IaCOps Framework in Augment Code VS Code IDE.
