# =========================================================================
# Development Environment Configuration for DNS Management Automation v2
# =========================================================================
# This file contains development-specific configuration variables
# It includes JIRA integration settings for the UAT environment
#
# Framework: Operational Excellence Automation Framework (OXAF)
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

# =========================
# Development Environment Settings
# =========================

# Environment identification
var_environment: "development"
deployment_mode: "development"

# =========================
# Development Integration Configuration
# =========================

# ITSM Integration for development
itsm_integration:
  enabled: true
  system: "jira"
  update_tickets: true
  create_change_requests: false
  approval_required: false
  var_environment: "development"

  # Development JIRA Configuration (uses UAT instance)
  jira_config:
    instance: "uat"
    base_url: "https://jsd-uat.hcloud.healthgrp.com.sg"
    project_key: "SR"

    # Development-specific settings
    timeout_seconds: 20
    retry_attempts: 2
    validate_ssl: false

    # Minimal error handling for development
    ignore_errors: true
    log_failures: true
    notify_on_failure: false
    escalate_on_failure: false

# Development JIRA Credentials (CyberArk collection-based)
jira_credentials:
  # CyberArk account names for credential retrieval
  username_account: "JIRA_UAT_USERNAME"
  password_account: "JIRA_UAT_PASSWORD"
  grid_token_account: "JIRA_UAT_GRID_TOKEN"

  # API endpoints
  api_base_url: "https://jsd-uat.hcloud.healthgrp.com.sg/rest/api/2"
  grid_base_url: "https://jsd-uat.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid"

# =========================
# Development Notification Configuration
# =========================

# Development notification recipients
notification_recipients:
  default: "{{ var_notification_email | default('<EMAIL>') }}"
  escalation: "{{ var_escalation_email | default('<EMAIL>') }}"
  security: "{{ var_security_email | default('<EMAIL>') }}"
  developers: "{{ var_developers_email | default('<EMAIL>') }}"

# =========================
# Development Testing Configuration
# =========================

# Enhanced testing for development
testing_configuration:
  # Test data settings
  use_test_data: true
  test_domain_suffix: ".dev.test"
  test_ip_range: "10.0.0.0/16"

  # Test execution settings
  enable_dry_run: true
  validate_before_execution: true
  cleanup_after_test: true
  mock_external_calls: true

  # Test reporting
  generate_test_reports: true
  test_report_format: "json"
  test_report_retention_days: 7

  # Development-specific testing
  enable_unit_tests: true
  enable_integration_tests: true
  enable_performance_tests: false
  test_coverage_required: 80

# =========================
# Development Monitoring Configuration
# =========================

# Development monitoring integration
monitoring_integration:
  enabled: false
  platforms: []
  metrics_endpoint: ""
  alerting_enabled: false
  dashboard_enabled: false

# =========================
# Development Compliance Configuration
# =========================

# Minimal compliance for development
compliance_configuration:
  frameworks:
    hipaa: false
    pci_dss: false
    sox: false
    iso27001: false

  # Minimal audit requirements for development
  audit_requirements:
    change_tracking: false
    approval_workflows: false
    segregation_of_duties: false
    data_retention: false
    real_time_monitoring: false

# =========================
# Development Security Configuration
# =========================

# Development security settings
dns_security:
  access_control:
    enforce_rbac: false
    require_mfa: false
    audit_all_access: false
    session_timeout: 7200

  encryption:
    encrypt_communications: false
    encrypt_logs: false
    encrypt_credentials: true

  monitoring:
    log_security_events: false
    alert_on_anomalies: false
    integrate_with_siem: false

# =========================
# Development Performance Configuration
# =========================

# Development performance settings
dns_performance:
  execution_timeout: 600
  dns_query_timeout: 10
  retry_attempts: 1
  parallel_execution: false

  # Resource limits
  max_concurrent_operations: 1
  memory_limit_mb: 256
  cpu_limit_percent: 25

# =========================
# Development Debugging Configuration
# =========================

# Enhanced debugging for development
debugging_configuration:
  # Debug settings
  enable_debug_mode: true
  debug_level: "TRACE"
  log_all_variables: true
  log_execution_steps: true

  # Development tools
  enable_profiling: true
  enable_memory_tracking: true
  enable_performance_monitoring: true

  # Debug output
  debug_output_format: "json"
  debug_output_location: "C:\\OE_AAP_LOGS\\DEV\\DEBUG"
  retain_debug_logs: true

# =========================
# Development Backup Configuration
# =========================

# Development backup settings
backup_configuration:
  enabled: false
  frequency: "never"
  retention_days: 7
  backup_location: "/backup/development"

# Recovery settings
recovery_configuration:
  automatic_rollback: false
  manual_rollback_available: true
  point_in_time_recovery: false
  disaster_recovery_enabled: false

# =========================
# Development Maintenance Configuration
# =========================

# Maintenance windows for development
maintenance_windows:
  development:
    start_time: "00:00"
    end_time: "23:59"
    timezone: "Asia/Singapore"
    days: ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday"]

# Health checks for development
health_checks:
  enabled: false
  frequency_minutes: 60
  timeout_seconds: 30
  alert_on_failure: false

# =========================
# Development Data Protection
# =========================

# Data protection for development
data_protection:
  encryption_at_rest: false
  encryption_in_transit: false
  data_classification: "public"
  retention_period_days: 30  # 1 month

# =========================
# Development Logging Configuration
# =========================

# Enhanced logging for development
dns_log_config:
  enabled: true
  level: "TRACE"
  format: "json"
  directory: "C:\\OE_AAP_LOGS\\DEV"
  file_name: "{{ tower_job_id | default('LOCAL') }}_{{ ansible_date_time.day }}{{ ansible_date_time.month }}{{ ansible_date_time.year }}_{{ var_sr_number | default('NOTICKET') }}_DNS_DEV.log"

  # Development-specific logging
  log_test_data: true
  log_performance_metrics: true
  log_debug_information: true
  log_variable_values: true
  log_execution_flow: true

  # Log retention
  retention_days: 30
  compress_old_logs: false
  archive_location: "C:\\OE_AAP_LOGS\\DEV\\ARCHIVE"

# =========================
# Development Feature Flags
# =========================

# Feature flags for development
feature_flags:
  # Experimental features
  enable_experimental_features: true
  enable_beta_features: true
  enable_preview_features: true

  # Development-specific features
  enable_mock_mode: true
  enable_simulation_mode: true
  enable_test_mode: true

  # Performance features
  enable_caching: false
  enable_optimization: false
  enable_parallel_processing: false
