# =========================================================================
# JIRA Integration Validation and Testing Module
# =========================================================================
# This module validates JIRA connectivity, authentication, and permissions
# before attempting DNS automation operations with JIRA integration
#
# Framework: Operational Excellence Automation Framework (OXAF)
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

---
# =========================
# JIRA Integration Validation - Entry Point
# =========================

- name: "JIRA Validation - Initialize Validation Process"
  debug:
    msg: |
      JIRA Integration Validation Starting
      ===================================
      Environment: {{ environment | default('production') }}
      JIRA Integration Enabled: {{ itsm_integration.enabled | default(false) }}
      JIRA Instance: {{ jira_instance_config.instance_name | default('unknown') }}
      Base URL: {{ jira_instance_config.base_url | default('unknown') }}
  when: 
    - itsm_integration.enabled | default(false) | bool
    - jira_testing.validate_connectivity | default(true) | bool

# =========================
# JIRA Configuration Validation
# =========================

- name: "JIRA Validation - Validate Configuration Variables"
  assert:
    that:
      - jira_credentials is defined
      - jira_credentials.username is defined
      - jira_credentials.password is defined
      - jira_credentials.api_base_url is defined
      - jira_credentials.grid_base_url is defined
    fail_msg: "Missing required JIRA configuration variables"
    success_msg: "JIRA configuration validation passed"
  when: 
    - itsm_integration.enabled | default(false) | bool
    - jira_testing.validate_connectivity | default(true) | bool

- name: "JIRA Validation - Validate Environment Mapping"
  assert:
    that:
      - jira_environment_mapping is defined
      - jira_environment_mapping[environment | default('production')] is defined
    fail_msg: "Missing JIRA environment mapping for {{ environment | default('production') }}"
    success_msg: "JIRA environment mapping validation passed"
  when: 
    - itsm_integration.enabled | default(false) | bool
    - jira_testing.validate_connectivity | default(true) | bool

# =========================
# JIRA Connectivity Testing
# =========================

- name: "JIRA Validation - Test JIRA REST API Connectivity"
  uri:
    url: "{{ jira_credentials.api_base_url }}/myself"
    headers:
      Authorization: "Basic {{ (jira_credentials.username + ':' + jira_credentials.password) | b64encode }}"
      Content-Type: "application/json"
    method: GET
    status_code: [200]
    timeout: "{{ itsm_integration.jira_config.timeout_seconds | default(30) }}"
    validate_certs: "{{ itsm_integration.jira_config.validate_ssl | default(false) }}"
  register: jira_connectivity_test
  when: 
    - itsm_integration.enabled | default(false) | bool
    - jira_testing.validate_connectivity | default(true) | bool
  ignore_errors: true

- name: "JIRA Validation - Test JIRA Grid API Connectivity"
  uri:
    url: "{{ jira_credentials.grid_base_url }}"
    headers:
      Authorization: "{{ jira_credentials.grid_token }}"
      Content-Type: "application/json"
    method: GET
    status_code: [200, 404]  # 404 is acceptable for base grid URL
    timeout: "{{ itsm_integration.jira_config.timeout_seconds | default(30) }}"
    validate_certs: "{{ itsm_integration.jira_config.validate_ssl | default(false) }}"
  register: jira_grid_connectivity_test
  when: 
    - itsm_integration.enabled | default(false) | bool
    - jira_testing.validate_connectivity | default(true) | bool
  ignore_errors: true

# =========================
# JIRA Authentication Validation
# =========================

- name: "JIRA Validation - Validate REST API Authentication"
  set_fact:
    jira_rest_auth_valid: "{{ jira_connectivity_test is succeeded }}"
    jira_rest_auth_error: "{{ jira_connectivity_test.msg | default('Unknown error') }}"
  when: 
    - itsm_integration.enabled | default(false) | bool
    - jira_testing.validate_authentication | default(true) | bool

- name: "JIRA Validation - Validate Grid API Authentication"
  set_fact:
    jira_grid_auth_valid: "{{ jira_grid_connectivity_test is succeeded }}"
    jira_grid_auth_error: "{{ jira_grid_connectivity_test.msg | default('Unknown error') }}"
  when: 
    - itsm_integration.enabled | default(false) | bool
    - jira_testing.validate_authentication | default(true) | bool

# =========================
# JIRA Permissions Testing
# =========================

- name: "JIRA Validation - Test Service Request Access (if test ticket provided)"
  uri:
    url: "{{ jira_credentials.api_base_url }}/issue/{{ jira_testing.test_ticket_prefix | default('TEST-') }}123456"
    headers:
      Authorization: "Basic {{ (jira_credentials.username + ':' + jira_credentials.password) | b64encode }}"
      Content-Type: "application/json"
    method: GET
    status_code: [200, 404]  # 404 is acceptable for non-existent test ticket
    timeout: "{{ itsm_integration.jira_config.timeout_seconds | default(30) }}"
    validate_certs: "{{ itsm_integration.jira_config.validate_ssl | default(false) }}"
  register: jira_permissions_test
  when: 
    - itsm_integration.enabled | default(false) | bool
    - jira_testing.validate_permissions | default(true) | bool
    - jira_testing.test_ticket_prefix is defined
  ignore_errors: true

- name: "JIRA Validation - Test Grid API Permissions (if test grid provided)"
  uri:
    url: "{{ jira_credentials.grid_base_url }}/{{ jira_testing.test_grid_id | default('999999') }}/issue/TEST-123456/"
    headers:
      Authorization: "{{ jira_credentials.grid_token }}"
      Content-Type: "application/json"
    method: GET
    status_code: [200, 404, 400]  # Various acceptable responses for test
    timeout: "{{ itsm_integration.jira_config.timeout_seconds | default(30) }}"
    validate_certs: "{{ itsm_integration.jira_config.validate_ssl | default(false) }}"
  register: jira_grid_permissions_test
  when: 
    - itsm_integration.enabled | default(false) | bool
    - jira_testing.validate_permissions | default(true) | bool
    - jira_testing.test_grid_id is defined
  ignore_errors: true

# =========================
# JIRA Validation Results Analysis
# =========================

- name: "JIRA Validation - Analyze Validation Results"
  set_fact:
    jira_validation_results:
      connectivity:
        rest_api: "{{ jira_connectivity_test is succeeded }}"
        grid_api: "{{ jira_grid_connectivity_test is succeeded }}"
      authentication:
        rest_api: "{{ jira_rest_auth_valid | default(false) }}"
        grid_api: "{{ jira_grid_auth_valid | default(false) }}"
      permissions:
        rest_api: "{{ jira_permissions_test is succeeded | default(true) }}"
        grid_api: "{{ jira_grid_permissions_test is succeeded | default(true) }}"
      overall_status: >-
        {{
          'PASS' if (
            (jira_connectivity_test is succeeded) and
            (jira_grid_connectivity_test is succeeded) and
            (jira_rest_auth_valid | default(false)) and
            (jira_grid_auth_valid | default(false))
          ) else 'FAIL'
        }}
      errors:
        rest_api: "{{ jira_rest_auth_error | default('None') }}"
        grid_api: "{{ jira_grid_auth_error | default('None') }}"
      timestamp: "{{ ansible_date_time.iso8601 }}"
  when: 
    - itsm_integration.enabled | default(false) | bool

# =========================
# JIRA Validation Reporting
# =========================

- name: "JIRA Validation - Report Successful Validation"
  debug:
    msg: |
      JIRA Integration Validation - SUCCESS
      ====================================
      Environment: {{ environment | default('production') }}
      JIRA Instance: {{ jira_instance_config.base_url | default('unknown') }}
      
      Connectivity Tests:
      - REST API: {{ 'PASS' if jira_validation_results.connectivity.rest_api else 'FAIL' }}
      - Grid API: {{ 'PASS' if jira_validation_results.connectivity.grid_api else 'FAIL' }}
      
      Authentication Tests:
      - REST API: {{ 'PASS' if jira_validation_results.authentication.rest_api else 'FAIL' }}
      - Grid API: {{ 'PASS' if jira_validation_results.authentication.grid_api else 'FAIL' }}
      
      Permission Tests:
      - REST API: {{ 'PASS' if jira_validation_results.permissions.rest_api else 'FAIL' }}
      - Grid API: {{ 'PASS' if jira_validation_results.permissions.grid_api else 'FAIL' }}
      
      Overall Status: {{ jira_validation_results.overall_status }}
      
      JIRA integration is ready for DNS automation operations.
  when: 
    - itsm_integration.enabled | default(false) | bool
    - jira_validation_results.overall_status == 'PASS'

- name: "JIRA Validation - Report Failed Validation"
  debug:
    msg: |
      JIRA Integration Validation - FAILED
      ===================================
      Environment: {{ environment | default('production') }}
      JIRA Instance: {{ jira_instance_config.base_url | default('unknown') }}
      
      Connectivity Issues:
      - REST API: {{ 'FAIL - ' + jira_validation_results.errors.rest_api if not jira_validation_results.connectivity.rest_api else 'PASS' }}
      - Grid API: {{ 'FAIL - ' + jira_validation_results.errors.grid_api if not jira_validation_results.connectivity.grid_api else 'PASS' }}
      
      Authentication Issues:
      - REST API: {{ 'FAIL' if not jira_validation_results.authentication.rest_api else 'PASS' }}
      - Grid API: {{ 'FAIL' if not jira_validation_results.authentication.grid_api else 'PASS' }}
      
      Overall Status: {{ jira_validation_results.overall_status }}
      
      JIRA integration issues detected. DNS automation will continue but JIRA updates may fail.
      Please verify JIRA configuration and credentials.
  when: 
    - itsm_integration.enabled | default(false) | bool
    - jira_validation_results.overall_status == 'FAIL'

# =========================
# JIRA Validation Error Handling
# =========================

- name: "JIRA Validation - Handle Critical JIRA Errors"
  fail:
    msg: |
      Critical JIRA Integration Error
      ==============================
      JIRA integration validation failed with critical errors.
      
      Error Details:
      - REST API Error: {{ jira_validation_results.errors.rest_api }}
      - Grid API Error: {{ jira_validation_results.errors.grid_api }}
      
      DNS automation cannot proceed with JIRA integration enabled.
      Please fix JIRA configuration or disable JIRA integration.
  when: 
    - itsm_integration.enabled | default(false) | bool
    - jira_validation_results.overall_status == 'FAIL'
    - not (itsm_integration.jira_config.ignore_errors | default(true) | bool)

# =========================
# JIRA Validation Metrics Collection
# =========================

- name: "JIRA Validation - Collect Validation Metrics"
  set_fact:
    jira_validation_metrics:
      validation_attempted: true
      validation_successful: "{{ jira_validation_results.overall_status == 'PASS' }}"
      connectivity_test_duration: "{{ jira_connectivity_test.elapsed | default(0) }}"
      grid_test_duration: "{{ jira_grid_connectivity_test.elapsed | default(0) }}"
      total_validation_time: "{{ (jira_connectivity_test.elapsed | default(0)) + (jira_grid_connectivity_test.elapsed | default(0)) }}"
      environment: "{{ environment | default('production') }}"
      jira_instance: "{{ jira_instance_config.instance_name | default('unknown') }}"
      timestamp: "{{ ansible_date_time.iso8601 }}"
      validation_details: "{{ jira_validation_results }}"
  when: 
    - itsm_integration.enabled | default(false) | bool
    - jira_metrics.track_response_time | default(false) | bool

# =========================
# JIRA Validation Completion
# =========================

- name: "JIRA Validation - Validation Process Complete"
  debug:
    msg: |
      JIRA Integration Validation Complete
      ===================================
      Status: {{ jira_validation_results.overall_status | default('SKIPPED') }}
      Environment: {{ environment | default('production') }}
      JIRA Instance: {{ jira_instance_config.base_url | default('unknown') }}
      
      DNS automation will proceed with JIRA integration {{ 'enabled' if (jira_validation_results.overall_status == 'PASS') else 'disabled due to errors' }}.
  when: 
    - itsm_integration.enabled | default(false) | bool
