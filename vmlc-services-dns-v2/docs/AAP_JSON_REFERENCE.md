# AAP Extra Variables - JSON Reference Guide

## Quick Reference for DNS Management Automation v2

**Framework:** Operational Excellence Automation Framework (OXAF)
**Version:** 2.0
**Purpose:** Comprehensive JSON examples for AAP execution

---

## 🚀 **Quick Start JSON Templates**

### Copy-Paste Ready Templates

#### **DNS Record Addition (Production)**
```json
{
  "action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "YOUR_HOSTNAME",
  "ipaddress": "YOUR_IP_ADDRESS",
  "ttl": 3600,
  "var_sr_number": "YOUR_SR_NUMBER",
  "testing_mode": false
}
```

#### **DNS Record Verification**
```json
{
  "action": "verify",
  "domain": "healthgrp.com.sg",
  "hostname": "YOUR_HOSTNAME",
  "var_sr_number": "YOUR_SR_NUMBER",
  "testing_mode": false
}
```

#### **DNS Record Removal**
```json
{
  "action": "remove",
  "domain": "healthgrp.com.sg",
  "hostname": "YOUR_HOSTNAME",
  "var_sr_number": "YOUR_SR_NUMBER",
  "testing_mode": false
}
```

#### **DNS Record Update**
```json
{
  "action": "update",
  "domain": "healthgrp.com.sg",
  "hostname": "YOUR_HOSTNAME",
  "ipaddress": "YOUR_NEW_IP_ADDRESS",
  "ttl": 3600,
  "var_sr_number": "YOUR_SR_NUMBER",
  "testing_mode": false
}
```

#### **DNS Record Synchronization**
```json
{
  "action": "sync",
  "domain": "healthgrp.com.sg",
  "hostname": "YOUR_HOSTNAME",
  "var_sr_number": "YOUR_SR_NUMBER",
  "testing_mode": false
}
```

---

## 📋 **Variable Reference Table**

| Variable | Type | Required | Default | Description |
|----------|------|----------|---------|-------------|
| `action` | String | ✅ Yes | N/A | DNS operation: verify, add, remove, update, sync |
| `domain` | String | ✅ Yes | N/A | Target DNS domain (e.g., healthgrp.com.sg) |
| `hostname` | String | ✅ Yes | N/A | Target hostname (without domain) |
| `ipaddress` | String | ⚠️ Conditional | N/A | Required for add/update operations |
| `ttl` | Integer | ❌ No | 3600 | Time-to-live in seconds |
| `var_sr_number` | String | ✅ Yes | N/A | JIRA ticket number for tracking (SR-XXXXXX, SCR-XXXXXX, or INC-XXXXXX) |
| `testing_mode` | Boolean | ❌ No | false | Enable dry-run mode |
| `environment` | String | ❌ No | production | Target environment override |

---

## 🎫 **JIRA Ticket Type Examples**

### **Service Request (SR) - Standard DNS Operations**
```json
{
  "action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "webserver01",
  "ipaddress": "*************",
  "ttl": 3600,
  "var_sr_number": "SR-123456",
  "testing_mode": false
}
```

### **Service Change Request (SCR) - Infrastructure Changes**
```json
{
  "action": "update",
  "domain": "healthgrp.com.sg",
  "hostname": "dbserver01",
  "ipaddress": "************",
  "ttl": 7200,
  "var_sr_number": "SCR-789012",
  "testing_mode": false
}
```

### **Incident (INC) - Emergency DNS Fixes**
```json
{
  "action": "remove",
  "domain": "healthgrp.com.sg",
  "hostname": "compromised-server",
  "var_sr_number": "INC-345678",
  "testing_mode": false
}
```

---

## 🌍 **Environment-Specific Examples**

### **Development Environment**
```json
{
  "action": "add",
  "domain": "devhealthgrp.com.sg",
  "hostname": "devserver01",
  "ipaddress": "**********",
  "ttl": 300,
  "environment": "development",
  "testing_mode": true,
  "var_sr_number": "SCR-DEV-001"
}
```

### **Staging Environment**
```json
{
  "action": "add",
  "domain": "stghealthgrp.com.sg",
  "hostname": "stgserver01",
  "ipaddress": "***************",
  "ttl": 1800,
  "environment": "staging",
  "testing_mode": false,
  "var_sr_number": "SR-STG-001"
}
```

### **Production Environment**
```json
{
  "action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "prodserver01",
  "ipaddress": "************",
  "ttl": 3600,
  "environment": "production",
  "testing_mode": false,
  "var_sr_number": "SR-PROD-001"
}
```

---

## 🔧 **Advanced Configuration Examples**

### **Enhanced Logging and Audit**
```json
{
  "action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "auditserver01",
  "ipaddress": "*************",
  "ttl": 3600,
  "enhanced_logging": true,
  "audit_level": "detailed",
  "var_sr_number": "SR-AUDIT-001",
  "testing_mode": false
}
```

### **Rollback Enabled Operation**
```json
{
  "action": "update",
  "domain": "healthgrp.com.sg",
  "hostname": "criticalserver01",
  "ipaddress": "*************",
  "ttl": 3600,
  "enable_rollback": true,
  "rollback_timeout": 300,
  "var_sr_number": "SR-CRITICAL-001",
  "testing_mode": false
}
```

### **Validation Before Execution**
```json
{
  "action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "validatedserver01",
  "ipaddress": "*************",
  "ttl": 3600,
  "validate_before_execution": true,
  "var_sr_number": "SR-VALIDATE-001",
  "testing_mode": false
}
```

---

## 📊 **Common Use Case Examples**

### **Web Server Deployment**
```json
{
  "action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "web01",
  "ipaddress": "*************",
  "ttl": 3600,
  "var_sr_number": "SR-WEB-001",
  "testing_mode": false,
  "execution_context": {
    "purpose": "Web server deployment",
    "team": "Infrastructure Team"
  }
}
```

### **Database Server Migration**
```json
{
  "action": "update",
  "domain": "healthgrp.com.sg",
  "hostname": "db01",
  "ipaddress": "************",
  "ttl": 7200,
  "var_sr_number": "SR-DB-MIGRATE-001",
  "testing_mode": false,
  "execution_context": {
    "purpose": "Database server migration",
    "maintenance_window": "2024-01-15 02:00:00 SGT"
  }
}
```

### **Server Decommissioning**
```json
{
  "action": "remove",
  "domain": "healthgrp.com.sg",
  "hostname": "legacy01",
  "var_sr_number": "SR-DECOM-001",
  "testing_mode": false,
  "execution_context": {
    "purpose": "Server decommissioning",
    "approval_reference": "CHANGE-2024-001"
  }
}
```

### **Load Balancer Configuration**
```json
{
  "action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "lb01",
  "ipaddress": "************",
  "ttl": 300,
  "var_sr_number": "SR-LB-001",
  "testing_mode": false,
  "execution_context": {
    "purpose": "Load balancer setup",
    "service_type": "critical"
  }
}
```

---

## 🔄 **Post-Zone Creation Scenarios**

### **Zone Created by DNS Engineers**
```json
{
  "action": "sync",
  "domain": "newapp.healthgrp.com.sg",
  "hostname": "app01",
  "ipaddress": "*************",
  "ttl": 3600,
  "var_sr_number": "SR-ZONE-SYNC-001",
  "testing_mode": false,
  "execution_context": {
    "purpose": "Post-zone creation synchronization",
    "zone_created_by": "DNS Engineering Team"
  }
}
```

### **Missing PTR Record Fix**
```json
{
  "action": "sync",
  "domain": "healthgrp.com.sg",
  "hostname": "fixptr01",
  "var_sr_number": "SR-PTR-FIX-001",
  "testing_mode": false,
  "execution_context": {
    "purpose": "Fix missing PTR record",
    "issue_reference": "INC-2024-001"
  }
}
```

---

## 🧪 **Testing and Validation Examples**

### **Dry Run Testing**
```json
{
  "action": "add",
  "domain": "healthgrp.com.sg",
  "hostname": "testserver01",
  "ipaddress": "*************",
  "ttl": 300,
  "testing_mode": true,
  "var_sr_number": "SR-TEST-001",
  "execution_context": {
    "purpose": "Testing DNS automation",
    "tester": "<EMAIL>"
  }
}
```

### **Development Testing**
```json
{
  "action": "add",
  "domain": "devhealthgrp.com.sg",
  "hostname": "unittest01",
  "ipaddress": "***********",
  "ttl": 60,
  "environment": "development",
  "testing_mode": true,
  "var_sr_number": "SR-UNITTEST-001"
}
```

---

## 📝 **JSON Validation Checklist**

### **Required Fields Validation**
- ✅ `action` - Must be one of: verify, add, remove, update, sync
- ✅ `domain` - Must be valid domain format
- ✅ `hostname` - Must be valid hostname (no domain suffix)
- ✅ `var_sr_number` - Must follow JIRA ticket format: SR-XXXXXX, SCR-XXXXXX, or INC-XXXXXX
- ⚠️ `ipaddress` - Required for add/update operations

### **Optional Fields Best Practices**
- 🔧 `ttl` - Use 300 for testing, 3600 for production
- 🔧 `testing_mode` - Always use true for initial testing
- 🔧 `environment` - Specify for non-production environments
- 🔧 `execution_context` - Add for audit trail enhancement

### **Common JSON Errors to Avoid**
- ❌ Missing quotes around string values
- ❌ Trailing commas in JSON objects
- ❌ Invalid boolean values (use true/false, not True/False)
- ❌ Missing required fields for specific operations
- ❌ Invalid IP address formats

---

## 🎯 **Quick Copy Templates by Operation**

### **VERIFY Operation**
```json
{"action":"verify","domain":"healthgrp.com.sg","hostname":"HOSTNAME","var_sr_number":"SR-NUMBER","testing_mode":false}
```

### **ADD Operation**
```json
{"action":"add","domain":"healthgrp.com.sg","hostname":"HOSTNAME","ipaddress":"IP_ADDRESS","ttl":3600,"var_sr_number":"SR-NUMBER","testing_mode":false}
```

### **REMOVE Operation**
```json
{"action":"remove","domain":"healthgrp.com.sg","hostname":"HOSTNAME","var_sr_number":"SR-NUMBER","testing_mode":false}
```

### **UPDATE Operation**
```json
{"action":"update","domain":"healthgrp.com.sg","hostname":"HOSTNAME","ipaddress":"NEW_IP_ADDRESS","ttl":3600,"var_sr_number":"SR-NUMBER","testing_mode":false}
```

### **SYNC Operation**
```json
{"action":"sync","domain":"healthgrp.com.sg","hostname":"HOSTNAME","var_sr_number":"SR-NUMBER","testing_mode":false}
```

---

## 📞 **Support and Troubleshooting**

### **Common Issues**
1. **JSON Syntax Error:** Validate JSON format using online validators
2. **Missing Required Fields:** Check operation-specific requirements
3. **Invalid IP Address:** Ensure proper IPv4 format (x.x.x.x)
4. **Domain Format Error:** Use FQDN without trailing dot

### **Validation Tools**
- **JSON Validator:** https://jsonlint.com/
- **IP Address Validator:** Built into script validation
- **Domain Format Checker:** Built into script validation

### **Support Contacts**
- **DNS Automation Team:** <EMAIL>
- **Infrastructure Team:** <EMAIL>
- **Emergency Support:** Available 24/7 through ITSM

---

*This JSON reference guide is part of the DNS Management Automation v2 solution developed by the CES Operational Excellence Team following Operational Excellence Automation Framework (OXAF) patterns.*
