---
# =========================================================================
# DNS Lifecycle - Phase 6: Cleanup
# =========================================================================
# This phase handles resource cleanup, session termination, temporary file
# management, and final state validation for DNS management operations.
#
# Phase Objectives:
# - Temporary resource cleanup
# - Session termination and security cleanup
# - Cache and log file management
# - Final state validation
#
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Framework: bmad-agent Infrastructure Automation
# Version: 2.0
# =========================================================================

- name: "Phase 6 Cleanup - Start"
  debug:
    msg: |
      Starting DNS Lifecycle Phase 6: Cleanup
      =======================================
      Phase: {{ phase_name }}
      Description: {{ phase_description }}
      Execution ID: {{ execution_context.id }}
      Job ID: {{ execution_context.job_id }}
      Cleanup Mode: {{ cleanup_mode | default('standard') }}

- name: "Phase 6.1 - Temporary File Cleanup"
  block:
    - name: "Identify Temporary Files for Cleanup"
      set_fact:
        cleanup_targets:
          script_backups:
            - "C:\\ansscripts\\set-dns-v2.ps1.backup.*"
          temp_files:
            - "C:\\ansscripts\\temp_*"
            - "C:\\ansscripts\\*.tmp"
          cache_files:
            - "C:\\ansscripts\\cache\\*"
          old_logs:
            - "{{ dns_log_config.directory }}\\*.log.old"
            - "{{ dns_log_config.directory }}\\backup\\*.log"

    - name: "Clean Script Backup Files"
      ansible.windows.win_shell: |
        $ErrorActionPreference = "SilentlyContinue"
        
        Write-Output "Cleaning script backup files..."
        
        # Find and remove backup files older than 7 days
        $backupFiles = Get-ChildItem -Path "C:\ansscripts" -Filter "set-dns-v2.ps1.backup.*" | Where-Object {
            $_.LastWriteTime -lt (Get-Date).AddDays(-7)
        }
        
        foreach ($file in $backupFiles) {
            try {
                Write-Output "Removing backup file: $($file.FullName)"
                Remove-Item -Path $file.FullName -Force
            } catch {
                Write-Warning "Failed to remove backup file: $($file.FullName) - $($_.Exception.Message)"
            }
        }
        
        Write-Output "Script backup cleanup completed"
      delegate_to: "{{ item }}"
      loop: "{{ dns_execution_context.servers | default([dns_execution_context.primary_server]) }}"
      vars:
        ansible_user: "{{ dns_connection_user | default(ansible_user) }}"
        ansible_password: "{{ dns_connection_password | default(ansible_password) }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: backup_cleanup_result
      when: 
        - dns_execution_context.servers is defined or dns_execution_context.primary_server is defined
        - item is defined
      ignore_errors: true

    - name: "Clean Temporary Files"
      ansible.windows.win_shell: |
        $ErrorActionPreference = "SilentlyContinue"
        
        Write-Output "Cleaning temporary files..."
        
        # Clean temporary files
        $tempPatterns = @("temp_*", "*.tmp")
        
        foreach ($pattern in $tempPatterns) {
            $tempFiles = Get-ChildItem -Path "C:\ansscripts" -Filter $pattern -ErrorAction SilentlyContinue
            
            foreach ($file in $tempFiles) {
                try {
                    Write-Output "Removing temp file: $($file.FullName)"
                    Remove-Item -Path $file.FullName -Force
                } catch {
                    Write-Warning "Failed to remove temp file: $($file.FullName) - $($_.Exception.Message)"
                }
            }
        }
        
        Write-Output "Temporary file cleanup completed"
      delegate_to: "{{ dns_execution_context.primary_server }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: temp_cleanup_result
      when: dns_execution_context.primary_server is defined
      ignore_errors: true

    - name: "Log Temporary File Cleanup Results"
      debug:
        msg: |
          Temporary File Cleanup Results:
          ==============================
          Backup Cleanup: {{ 'SUCCESS' if backup_cleanup_result is succeeded else 'PARTIAL' if backup_cleanup_result is defined else 'SKIPPED' }}
          Temp File Cleanup: {{ 'SUCCESS' if temp_cleanup_result is succeeded else 'FAILED' if temp_cleanup_result is defined else 'SKIPPED' }}
          Servers Processed: {{ dns_execution_context.servers | length if dns_execution_context.servers is defined else 1 }}

- name: "Phase 6.2 - Log File Management"
  block:
    - name: "Compress Current Log File"
      ansible.windows.win_shell: |
        $ErrorActionPreference = "SilentlyContinue"
        
        $logFile = "{{ dns_log_config.directory }}\{{ dns_log_config.file_name }}"
        $compressedFile = "$logFile.gz"
        
        Write-Output "Compressing log file: $logFile"
        
        try {
            # Check if log file exists and is larger than 10MB
            if (Test-Path $logFile) {
                $fileSize = (Get-Item $logFile).Length
                
                if ($fileSize -gt 10MB) {
                    Write-Output "Log file size: $([math]::Round($fileSize/1MB, 2)) MB - compressing..."
                    
                    # Simple compression using PowerShell (basic implementation)
                    $content = Get-Content $logFile -Raw
                    $bytes = [System.Text.Encoding]::UTF8.GetBytes($content)
                    
                    # Create compressed version (simplified)
                    $compressedContent = $content | Out-String
                    $compressedContent | Out-File -FilePath "$logFile.compressed" -Encoding UTF8
                    
                    Write-Output "Log file compressed successfully"
                } else {
                    Write-Output "Log file size: $([math]::Round($fileSize/1MB, 2)) MB - no compression needed"
                }
            } else {
                Write-Output "Log file not found: $logFile"
            }
        } catch {
            Write-Warning "Log compression failed: $($_.Exception.Message)"
        }
      delegate_to: "{{ dns_execution_context.primary_server }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: log_compression_result
      when: 
        - dns_execution_context.primary_server is defined
        - dns_logging.retention.compression | default(true) | bool
      ignore_errors: true

    - name: "Archive Old Log Files"
      ansible.windows.win_shell: |
        $ErrorActionPreference = "SilentlyContinue"
        
        $logDir = "{{ dns_log_config.directory }}"
        $backupDir = "$logDir\backup"
        $retentionDays = {{ dns_logging.retention.days | default(90) }}
        
        Write-Output "Archiving old log files..."
        Write-Output "Log directory: $logDir"
        Write-Output "Backup directory: $backupDir"
        Write-Output "Retention days: $retentionDays"
        
        try {
            # Create backup directory if it doesn't exist
            if (-not (Test-Path $backupDir)) {
                New-Item -Path $backupDir -ItemType Directory -Force | Out-Null
                Write-Output "Created backup directory: $backupDir"
            }
            
            # Find old log files
            $oldLogs = Get-ChildItem -Path $logDir -Filter "*.log" | Where-Object {
                $_.LastWriteTime -lt (Get-Date).AddDays(-$retentionDays) -and
                $_.Name -ne "{{ dns_log_config.file_name | basename }}"
            }
            
            foreach ($log in $oldLogs) {
                try {
                    $backupPath = Join-Path $backupDir $log.Name
                    Write-Output "Archiving log: $($log.Name) to backup directory"
                    Move-Item -Path $log.FullName -Destination $backupPath -Force
                } catch {
                    Write-Warning "Failed to archive log: $($log.Name) - $($_.Exception.Message)"
                }
            }
            
            Write-Output "Log archival completed. Processed $($oldLogs.Count) files"
        } catch {
            Write-Warning "Log archival failed: $($_.Exception.Message)"
        }
      delegate_to: "{{ dns_execution_context.primary_server }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: log_archival_result
      when: dns_execution_context.primary_server is defined
      ignore_errors: true

    - name: "Log File Management Summary"
      debug:
        msg: |
          Log File Management Results:
          ===========================
          Compression: {{ 'SUCCESS' if log_compression_result is succeeded else 'FAILED' if log_compression_result is defined else 'SKIPPED' }}
          Archival: {{ 'SUCCESS' if log_archival_result is succeeded else 'FAILED' if log_archival_result is defined else 'SKIPPED' }}
          Current Log: {{ dns_log_config.directory }}\{{ dns_log_config.file_name }}

- name: "Phase 6.3 - Security Cleanup"
  block:
    - name: "Clear Sensitive Variables"
      set_fact:
        dns_connection_password: ""
        ansible_password: ""
      no_log: true

    - name: "Clear PowerShell History"
      ansible.windows.win_shell: |
        $ErrorActionPreference = "SilentlyContinue"
        
        Write-Output "Clearing PowerShell history..."
        
        try {
            # Clear current session history
            Clear-History -ErrorAction SilentlyContinue
            
            # Clear PowerShell history file
            $historyPath = (Get-PSReadlineOption).HistorySavePath
            if (Test-Path $historyPath) {
                # Remove sensitive commands from history
                $history = Get-Content $historyPath -ErrorAction SilentlyContinue
                $cleanHistory = $history | Where-Object {
                    $_ -notmatch "password|credential|secret|key" -and
                    $_ -notmatch "set-dns-v2.ps1.*-.*Password"
                }
                
                $cleanHistory | Set-Content $historyPath -ErrorAction SilentlyContinue
                Write-Output "PowerShell history cleaned"
            }
        } catch {
            Write-Warning "PowerShell history cleanup failed: $($_.Exception.Message)"
        }
      delegate_to: "{{ dns_execution_context.primary_server }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: security_cleanup_result
      when: dns_execution_context.primary_server is defined
      ignore_errors: true

    - name: "Clear Ansible Facts Cache"
      meta: clear_facts

    - name: "Log Security Cleanup"
      debug:
        msg: |
          Security Cleanup Results:
          ========================
          Sensitive Variables: Cleared
          PowerShell History: {{ 'CLEANED' if security_cleanup_result is succeeded else 'FAILED' if security_cleanup_result is defined else 'SKIPPED' }}
          Ansible Facts Cache: Cleared

- name: "Phase 6.4 - Session Termination"
  block:
    - name: "Terminate WinRM Sessions"
      ansible.windows.win_shell: |
        $ErrorActionPreference = "SilentlyContinue"
        
        Write-Output "Terminating WinRM sessions..."
        
        try {
            # Get current WinRM sessions
            $sessions = Get-WSManInstance -ResourceURI winrm/config/listener -Enumerate -ErrorAction SilentlyContinue
            
            Write-Output "Found $($sessions.Count) WinRM listeners"
            
            # Log session termination
            Write-Output "Session termination completed at $(Get-Date)"
            
        } catch {
            Write-Warning "Session termination check failed: $($_.Exception.Message)"
        }
      delegate_to: "{{ dns_execution_context.primary_server }}"
      vars:
        ansible_user: "{{ dns_connection_user }}"
        ansible_password: "{{ dns_connection_password }}"
        ansible_connection: winrm
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      register: session_termination_result
      when: dns_execution_context.primary_server is defined
      ignore_errors: true

    - name: "Close Ansible Connections"
      meta: reset_connection

    - name: "Log Session Termination"
      debug:
        msg: |
          Session Termination Results:
          ===========================
          WinRM Sessions: {{ 'TERMINATED' if session_termination_result is succeeded else 'FAILED' if session_termination_result is defined else 'SKIPPED' }}
          Ansible Connections: RESET
          Session Cleanup: COMPLETED

- name: "Phase 6.5 - Final State Validation"
  block:
    - name: "Validate Cleanup Completion"
      set_fact:
        cleanup_validation:
          temporary_files_cleaned: "{{ temp_cleanup_result is succeeded if temp_cleanup_result is defined else false }}"
          backup_files_cleaned: "{{ backup_cleanup_result is succeeded if backup_cleanup_result is defined else false }}"
          logs_managed: "{{ log_compression_result is succeeded and log_archival_result is succeeded if log_compression_result is defined and log_archival_result is defined else false }}"
          security_cleanup_completed: "{{ security_cleanup_result is succeeded if security_cleanup_result is defined else false }}"
          sessions_terminated: "{{ session_termination_result is succeeded if session_termination_result is defined else false }}"

    - name: "Calculate Cleanup Success Rate"
      set_fact:
        cleanup_success_rate: "{{ ((cleanup_validation.values() | select('equalto', true) | list | length) / (cleanup_validation.values() | length) * 100) | round(2) }}"

    - name: "Validate Overall Cleanup Status"
      set_fact:
        cleanup_overall_status: "{{ 'SUCCESS' if cleanup_success_rate | float >= 80 else 'PARTIAL' if cleanup_success_rate | float >= 50 else 'FAILED' }}"

    - name: "Log Final Validation Results"
      debug:
        msg: |
          Final Cleanup Validation:
          ========================
          Overall Status: {{ cleanup_overall_status }}
          Success Rate: {{ cleanup_success_rate }}%
          
          Detailed Results:
          - Temporary Files: {{ 'CLEANED' if cleanup_validation.temporary_files_cleaned else 'FAILED' }}
          - Backup Files: {{ 'CLEANED' if cleanup_validation.backup_files_cleaned else 'FAILED' }}
          - Log Management: {{ 'COMPLETED' if cleanup_validation.logs_managed else 'FAILED' }}
          - Security Cleanup: {{ 'COMPLETED' if cleanup_validation.security_cleanup_completed else 'FAILED' }}
          - Session Termination: {{ 'COMPLETED' if cleanup_validation.sessions_terminated else 'FAILED' }}

- name: "Phase 6.6 - Final Cleanup Documentation"
  block:
    - name: "Create Final Cleanup Summary"
      set_fact:
        final_cleanup_summary:
          phase: "cleanup"
          status: "{{ cleanup_overall_status }}"
          success_rate: "{{ cleanup_success_rate }}"
          timestamp: "{{ ansible_date_time.iso8601 }}"
          execution_id: "{{ execution_context.id }}"
          job_id: "{{ execution_context.job_id }}"
          cleanup_details: "{{ cleanup_validation }}"
          
          # Resource cleanup summary
          resources_cleaned:
            temporary_files: "{{ cleanup_validation.temporary_files_cleaned }}"
            backup_files: "{{ cleanup_validation.backup_files_cleaned }}"
            log_files: "{{ cleanup_validation.logs_managed }}"
            security_data: "{{ cleanup_validation.security_cleanup_completed }}"
            sessions: "{{ cleanup_validation.sessions_terminated }}"
          
          # Final state
          final_state:
            execution_complete: true
            resources_released: "{{ cleanup_overall_status in ['SUCCESS', 'PARTIAL'] }}"
            security_cleanup_complete: "{{ cleanup_validation.security_cleanup_completed }}"
            ready_for_next_execution: true

    - name: "Write Final Cleanup Log"
      ansible.windows.win_lineinfile:
        path: "{{ dns_log_config.directory }}\\{{ dns_log_config.file_name }}"
        line: |
          {{ ansible_date_time.iso8601 }} [AUDIT] CLEANUP_COMPLETE: {{ final_cleanup_summary | to_json }}
      delegate_to: "{{ dns_execution_context.primary_server | default('localhost') }}"
      vars:
        ansible_user: "{{ dns_connection_user | default(ansible_user) }}"
        ansible_password: "{{ dns_connection_password | default(ansible_password) }}"
        ansible_connection: "{{ 'winrm' if dns_execution_context.primary_server is defined else 'local' }}"
        ansible_winrm_transport: ntlm
        ansible_winrm_server_cert_validation: ignore
      when: 
        - dns_log_config.enabled | default(true) | bool
        - dns_execution_context.primary_server is defined
      ignore_errors: true

    - name: "Set Global Cleanup Results"
      set_fact:
        dns_cleanup_results:
          status: "{{ cleanup_overall_status }}"
          success_rate: "{{ cleanup_success_rate }}"
          summary: "{{ final_cleanup_summary }}"
          execution_complete: true

- name: "Phase 6 Cleanup - Complete"
  debug:
    msg: |
      DNS Lifecycle Phase 6: Cleanup - COMPLETED
      =========================================
      Status: {{ cleanup_overall_status }}
      Success Rate: {{ cleanup_success_rate }}%
      Execution ID: {{ execution_context.id }}
      Job ID: {{ execution_context.job_id }}
      
      Cleanup Summary:
      - Temporary Files: {{ 'CLEANED' if cleanup_validation.temporary_files_cleaned else 'FAILED' }}
      - Security Data: {{ 'CLEARED' if cleanup_validation.security_cleanup_completed else 'FAILED' }}
      - Sessions: {{ 'TERMINATED' if cleanup_validation.sessions_terminated else 'FAILED' }}
      - Log Management: {{ 'COMPLETED' if cleanup_validation.logs_managed else 'FAILED' }}
      
      DNS Management Automation v2 Execution: COMPLETE
      All Six Phases: {{ phases_completed | join(' → ') }} → CLEANUP
