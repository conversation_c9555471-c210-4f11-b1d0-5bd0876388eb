# Ansible Playbook Creation Task

## Purpose

- To develop comprehensive Ansible playbooks following the six-phase lifecycle framework
- To implement idempotent automation with robust error handling and comprehensive logging
- To ensure compliance with Ansible Automation Platform (AAP) best practices and organizational standards
- To create maintainable, testable, and scalable automation solutions for infrastructure operations

## Instructions

1. **Requirements Analysis & Planning:**

   - Review automation requirements and scope definition
   - Identify target systems and infrastructure components
   - Analyze dependencies and integration requirements
   - Define success criteria and validation methods
   - Document assumptions and constraints
   - Plan playbook structure and organization

2. **Six-Phase Lifecycle Design:**

   **Phase 1: Configuration**
   - Design variable structure and hierarchy
   - Plan inventory organization and host grouping
   - Define credential management strategy
   - Create configuration templates and defaults
   - Plan environment-specific configurations

   **Phase 2: Loading**
   - Design dynamic inventory and host discovery
   - Plan credential retrieval and validation
   - Create configuration loading and validation tasks
   - Implement pre-execution checks and validations

   **Phase 3: Execution**
   - Design core automation tasks and workflows
   - Plan task sequencing and dependencies
   - Create progress tracking and status reporting
   - Ensure idempotent operation design

   **Phase 4: Error Handling**
   - Design comprehensive error detection and handling
   - Plan rollback procedures and cleanup tasks
   - Create error logging and notification mechanisms
   - Handle partial failure scenarios

   **Phase 5: Reporting**
   - Design execution reporting and status updates
   - Plan audit trail and compliance reporting
   - Create performance metrics collection
   - Implement notification and alerting

   **Phase 6: Cleanup**
   - Design resource cleanup and state management
   - Plan temporary file and cache cleanup
   - Create session termination procedures
   - Implement maintenance and housekeeping

3. **Playbook Structure Development:**

   - Create main playbook with clear structure
   - Develop modular roles and tasks
   - Implement variable management and templating
   - Design handler and notification mechanisms
   - Create comprehensive documentation

4. **Variable and Inventory Management:**

   - Design variable hierarchy (group_vars, host_vars, defaults)
   - Create inventory structure and host grouping
   - Implement dynamic inventory sources
   - Plan for environment-specific configurations
   - Document variable usage and dependencies

5. **Task Implementation:**

   - Develop core automation tasks using FQCN modules
   - Implement idempotent operations with proper conditionals
   - Create task validation and verification steps
   - Design progress tracking and status reporting
   - Ensure proper task naming and documentation

6. **Error Handling & Rollback:**

   - Implement comprehensive error detection
   - Create rollback procedures for each major operation
   - Design error logging with standardized formats
   - Plan for partial failure recovery
   - Test error scenarios and rollback procedures

7. **Logging & Monitoring Integration:**

   - Implement comprehensive logging with naming convention:
     `<AAP JOB ID>_<DDMMYYYY>_<TICKET>_<OPERATION>_ANSIBLE.log`
   - Store logs in designated location: `C:\OE_AAP_LOGS\`
   - Create monitoring integration points
   - Implement audit trail and compliance logging
   - Design performance metrics collection

8. **Security & Compliance:**

   - Implement credential management with vault integration
   - Use no_log for sensitive operations
   - Follow security best practices for automation
   - Ensure compliance with organizational policies
   - Document security considerations and controls

9. **Testing & Validation:**

   - Develop Molecule test scenarios
   - Create syntax and lint validation
   - Implement idempotency testing
   - Test error handling and rollback procedures
   - Validate against quality checklist

10. **AAP Integration:**

    - Create Job Templates with proper configuration
    - Design Workflow Templates for complex operations
    - Configure inventory sources and credentials
    - Implement survey specifications for user input
    - Test execution in AAP environment

11. **Documentation & Handoff:**

    - Create comprehensive playbook documentation
    - Document variable usage and configuration
    - Provide troubleshooting and maintenance guides
    - Create user guides for AAP execution
    - Document testing and validation procedures

## Quality Standards

### Ansible Best Practices
- Use FQCN (Fully Qualified Collection Names) for all modules
- Implement proper task naming and documentation
- Follow variable naming conventions
- Use handlers for service management
- Implement proper conditionals and loops

### Idempotency Requirements
- All tasks must be idempotent and safely re-executable
- Use appropriate modules and parameters for idempotent operations
- Implement proper state checking and validation
- Test idempotency with multiple executions

### Error Handling Standards
- Implement comprehensive error detection and handling
- Create clear error messages and logging
- Design rollback procedures for all major operations
- Test error scenarios and recovery procedures

### Logging Requirements
- Follow organizational logging standards and naming conventions
- Store logs in designated locations with proper permissions
- Implement structured logging with consistent formats
- Include audit trail and compliance information

## Output Deliverables

- **Ansible Playbook** with six-phase lifecycle implementation
- **Role Definitions** with modular task organization
- **Variable Documentation** with usage guidelines
- **Inventory Templates** with host grouping strategies
- **AAP Job Templates** with proper configuration
- **Testing Procedures** with Molecule test scenarios
- **Documentation Package** with user and maintenance guides
- **Security Configuration** with credential and vault integration

## Quality Validation

- Playbook passes ansible-lint validation without errors
- Molecule tests execute successfully across all scenarios
- Idempotency tests confirm safe re-execution
- Error handling scenarios are tested and validated
- Logging meets organizational standards and requirements
- Security best practices are implemented and documented
- AAP integration is tested and functional
- Documentation is comprehensive and current

## Integration Points

- **Infrastructure Architect:** Align with infrastructure design and requirements
- **Systems Engineer:** Coordinate on target system configuration and access
- **Platform Engineer:** Integrate with CI/CD pipelines and deployment workflows
- **Security Engineer:** Ensure security compliance and credential management
- **DevOps Engineer:** Coordinate monitoring and operational procedures
