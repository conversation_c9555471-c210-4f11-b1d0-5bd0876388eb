# =========================================================================
# JIRA Service Request Update Handler - DNS Lifecycle Role
# =========================================================================
# This handler provides DNS-specific JIRA integration for lifecycle events
# including operation status updates and DNS-specific context information
#
# Framework: Operational Excellence Automation Framework (OXAF)
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

---
# =========================
# DNS Lifecycle JIRA Integration - Entry Point
# =========================

- name: "DNS Lifecycle - Initialize JIRA Update"
  debug:
    msg: |
      DNS Lifecycle JIRA Integration Starting
      Operation: {{ action | default('unknown') }}
      Domain: {{ domain | default('N/A') }}
      Hostname: {{ hostname | default('N/A') }}
      Ticket Number: {{ var_sr_number | default('NOTICKET') }}
      Ticket Type: {{ var_sr_number.split('-')[0] | default('Unknown') }}
      DNS Operation Status: {{ dns_operation_status | default('unknown') }}
  when:
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"

# =========================
# DNS Operation Context Preparation
# =========================

- name: "DNS Lifecycle - Prepare DNS Operation Context"
  set_fact:
    dns_operation_context:
      operation: "{{ action | default('unknown') }}"
      domain: "{{ domain | default('N/A') }}"
      hostname: "{{ hostname | default('N/A') }}"
      fqdn: "{{ hostname | default('N/A') }}.{{ domain | default('N/A') }}"
      ip_address: "{{ ipaddress | default('N/A') }}"
      ttl: "{{ ttl | default('N/A') }}"
      status: "{{ dns_operation_status | default('completed') }}"
      environment: "{{ environment | default('production') }}"
      execution_time: "{{ ansible_date_time.iso8601 }}"
      job_id: "{{ tower_job_id | default(ansible_job_id | default('UNKNOWN')) }}"
      operator: "{{ ansible_user | default('automation') }}"
  when:
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"

# =========================
# DNS-Specific JIRA Message Templates
# =========================

- name: "DNS Lifecycle - Prepare DNS-Specific JIRA Messages"
  set_fact:
    dns_jira_messages:
      # Standard AAP job link (v1 compatibility)
      standard: "{{ aap_url | default(tower_host | default('https://aap.example.com')) }}/#/jobs/playbook/{{ dns_operation_context.job_id }}/"

      # Enhanced DNS operation summary
      enhanced: |
        DNS Automation - {{ dns_operation_context.operation | upper }} Operation

        Target: {{ dns_operation_context.fqdn }}
        IP Address: {{ dns_operation_context.ip_address }}
        TTL: {{ dns_operation_context.ttl }}
        Environment: {{ dns_operation_context.environment | upper }}
        Status: {{ dns_operation_context.status | upper }}

        Execution Details:
        - Job ID: {{ dns_operation_context.job_id }}
        - Operator: {{ dns_operation_context.operator }}
        - Timestamp: {{ dns_operation_context.execution_time }}
        - AAP Job: {{ aap_url | default(tower_host | default('https://aap.example.com')) }}/#/jobs/playbook/{{ dns_operation_context.job_id }}/

      # Concise status update
      status: "DNS {{ dns_operation_context.operation }} {{ dns_operation_context.status }} for {{ dns_operation_context.fqdn }} - Job: {{ dns_operation_context.job_id }}"
  when:
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"

# =========================
# DNS Operation Result Analysis
# =========================

- name: "DNS Lifecycle - Analyze DNS Operation Results"
  set_fact:
    dns_operation_summary:
      success_count: "{{ dns_results.success_count | default(0) }}"
      failure_count: "{{ dns_results.failure_count | default(0) }}"
      warning_count: "{{ dns_results.warning_count | default(0) }}"
      total_operations: "{{ dns_results.total_operations | default(1) }}"
      overall_status: >-
        {{
          'SUCCESS' if (dns_results.failure_count | default(0) == 0) else
          'PARTIAL_SUCCESS' if (dns_results.success_count | default(0) > 0) else
          'FAILED'
        }}
      has_warnings: "{{ (dns_results.warning_count | default(0) > 0) | bool }}"
      execution_duration: "{{ dns_results.execution_duration | default('N/A') }}"
  when:
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"

# =========================
# DNS-Specific JIRA Update Logic
# =========================

- name: "DNS Lifecycle - Update JIRA with DNS Operation Details"
  include_tasks: "{{ playbook_dir }}/roles/audit_logging/handlers/update_service_request.yml"
  vars:
    # Override default message with DNS-specific content
    jira_update_message: "{{ dns_jira_messages.standard }}"
    jira_enhanced_message: "{{ dns_jira_messages.enhanced }}"

    # Add DNS-specific context
    dns_operation_status: "{{ dns_operation_summary.overall_status }}"
    action: "{{ dns_operation_context.operation }}"
    domain: "{{ dns_operation_context.domain }}"
    hostname: "{{ dns_operation_context.hostname }}"
  when:
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"

# =========================
# DNS Operation Status-Specific Updates
# =========================

- name: "DNS Lifecycle - Add Success Comment to JIRA"
  uri:
    url: "{{ jira_credentials.api_base_url }}/issue/{{ var_sr_number }}/comment"
    headers:
      Authorization: "Basic {{ (jira_username + ':' + jira_password) | b64encode }}"
      Content-Type: "application/json"
    method: POST
    status_code: [200, 201]
    timeout: "{{ itsm_integration.jira_config.timeout_seconds | default(30) }}"
    validate_certs: "{{ itsm_integration.jira_config.validate_ssl | default(false) }}"
    body_format: json
    body: |
      {
        "body": "✅ DNS Operation Successful\n\nOperation: {{ dns_operation_context.operation | upper }}\nTarget: {{ dns_operation_context.fqdn }}\nIP: {{ dns_operation_context.ip_address }}\nStatus: {{ dns_operation_summary.overall_status }}\n\nExecution completed successfully with no errors."
      }
  register: dns_success_comment
  when:
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"
    - dns_operation_summary.overall_status == 'SUCCESS'
    - itsm_integration.jira_config.update_method | default('grid_api') == 'rest_api'
    - jira_username is defined
    - jira_password is defined
  ignore_errors: true

- name: "DNS Lifecycle - Add Warning Comment to JIRA"
  uri:
    url: "{{ jira_credentials.api_base_url }}/issue/{{ var_sr_number }}/comment"
    headers:
      Authorization: "Basic {{ (jira_username + ':' + jira_password) | b64encode }}"
      Content-Type: "application/json"
    method: POST
    status_code: [200, 201]
    timeout: "{{ itsm_integration.jira_config.timeout_seconds | default(30) }}"
    validate_certs: "{{ itsm_integration.jira_config.validate_ssl | default(false) }}"
    body_format: json
    body: |
      {
        "body": "⚠️ DNS Operation Completed with Warnings\n\nOperation: {{ dns_operation_context.operation | upper }}\nTarget: {{ dns_operation_context.fqdn }}\nIP: {{ dns_operation_context.ip_address }}\nStatus: {{ dns_operation_summary.overall_status }}\nWarnings: {{ dns_operation_summary.warning_count }}\n\nPlease review the execution logs for warning details."
      }
  register: dns_warning_comment
  when:
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"
    - dns_operation_summary.has_warnings | bool
    - itsm_integration.jira_config.update_method | default('grid_api') == 'rest_api'
    - jira_username is defined
    - jira_password is defined
  ignore_errors: true

- name: "DNS Lifecycle - Add Failure Comment to JIRA"
  uri:
    url: "{{ jira_credentials.api_base_url }}/issue/{{ var_sr_number }}/comment"
    headers:
      Authorization: "Basic {{ (jira_username + ':' + jira_password) | b64encode }}"
      Content-Type: "application/json"
    method: POST
    status_code: [200, 201]
    timeout: "{{ itsm_integration.jira_config.timeout_seconds | default(30) }}"
    validate_certs: "{{ itsm_integration.jira_config.validate_ssl | default(false) }}"
    body_format: json
    body: |
      {
        "body": "❌ DNS Operation Failed\n\nOperation: {{ dns_operation_context.operation | upper }}\nTarget: {{ dns_operation_context.fqdn }}\nIP: {{ dns_operation_context.ip_address }}\nStatus: {{ dns_operation_summary.overall_status }}\nFailures: {{ dns_operation_summary.failure_count }}\n\nPlease review the execution logs and consider manual intervention."
      }
  register: dns_failure_comment
  when:
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"
    - dns_operation_summary.overall_status == 'FAILED'
    - itsm_integration.jira_config.update_method | default('grid_api') == 'rest_api'
    - jira_username is defined
    - jira_password is defined
  ignore_errors: true

# =========================
# DNS Lifecycle Metrics Collection
# =========================

- name: "DNS Lifecycle - Collect DNS-Specific JIRA Metrics"
  set_fact:
    dns_jira_metrics:
      operation_type: "{{ dns_operation_context.operation }}"
      target_fqdn: "{{ dns_operation_context.fqdn }}"
      operation_status: "{{ dns_operation_summary.overall_status }}"
      jira_update_attempted: true
      jira_update_successful: "{{ jira_update_success | default(false) }}"
      additional_comments_added: >-
        {{
          (dns_success_comment is succeeded) or
          (dns_warning_comment is succeeded) or
          (dns_failure_comment is succeeded)
        }}
      environment: "{{ dns_operation_context.environment }}"
      timestamp: "{{ dns_operation_context.execution_time }}"
      sr_number: "{{ var_sr_number }}"
      job_id: "{{ dns_operation_context.job_id }}"
  when:
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"

# =========================
# DNS Lifecycle JIRA Integration Logging
# =========================

- name: "DNS Lifecycle - Log DNS JIRA Integration Results"
  debug:
    msg: |
      DNS Lifecycle JIRA Integration Summary
      =====================================
      Operation: {{ dns_operation_context.operation | upper }}
      Target: {{ dns_operation_context.fqdn }}
      IP Address: {{ dns_operation_context.ip_address }}
      Operation Status: {{ dns_operation_summary.overall_status }}

      JIRA Integration:
      - Ticket Number: {{ var_sr_number }}
      - Ticket Type: {{ var_sr_number.split('-')[0] | default('Unknown') }}
      - Update Successful: {{ jira_update_success | default(false) }}
      - Method Used: {{ jira_update_method_used | default('none') }}
      - Additional Comments: {{ dns_jira_metrics.additional_comments_added | default(false) }}

      Job Details:
      - Job ID: {{ dns_operation_context.job_id }}
      - Environment: {{ dns_operation_context.environment }}
      - Execution Time: {{ dns_operation_context.execution_time }}
      - AAP Job Link: {{ dns_jira_messages.standard }}
  when:
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"

# =========================
# DNS Lifecycle Error Handling
# =========================

- name: "DNS Lifecycle - Handle JIRA Integration Errors"
  debug:
    msg: |
      DNS Lifecycle JIRA Integration Error Handling
      ============================================
      Ticket Number: {{ var_sr_number }}
      Ticket Type: {{ var_sr_number.split('-')[0] | default('Unknown') }}
      DNS Operation: {{ dns_operation_context.operation }} for {{ dns_operation_context.fqdn }}

      Error Details:
      - Primary Update Failed: {{ jira_update_result is failed | default(false) }}
      - Comment Update Failed: {{ jira_comment_result is failed | default(false) }}
      - Additional Comments Failed: {{ (dns_success_comment is failed) or (dns_warning_comment is failed) or (dns_failure_comment is failed) }}

      Impact: DNS operation completed successfully, but JIRA ticket may require manual update
      Recommendation: Verify ticket status and update manually if necessary
  when:
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"
    - not (jira_update_success | default(false) | bool)

# =========================
# DNS Lifecycle JIRA Integration Completion
# =========================

- name: "DNS Lifecycle - JIRA Integration Complete"
  debug:
    msg: |
      DNS Lifecycle JIRA Integration Completed
      =======================================
      Operation: {{ dns_operation_context.operation | upper }}
      Target: {{ dns_operation_context.fqdn }}
      Status: {{ dns_operation_summary.overall_status }}
      Ticket Number: {{ var_sr_number }}
      Ticket Type: {{ var_sr_number.split('-')[0] | default('Unknown') }}
      JIRA Update: {{ 'SUCCESS' if (jira_update_success | default(false)) else 'FAILED' }}

      DNS automation execution and JIRA integration process complete.
  when:
    - itsm_integration.enabled | default(false) | bool
    - var_sr_number is defined
    - var_sr_number != "NOTICKET"
