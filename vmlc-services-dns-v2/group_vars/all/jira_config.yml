# =========================================================================
# JIRA Integration Configuration for DNS Management Automation v2
# =========================================================================
# This file contains JIRA-specific configuration variables for all environments
# It includes API endpoints, authentication settings, and integration parameters
#
# Framework: Operational Excellence Automation Framework (OXAF)
# Author: CES Operational Excellence Team
# Contributor: <PERSON> (7409)
# Version: 2.0
# =========================================================================

# =========================
# JIRA Environment Configuration
# =========================

# Default JIRA configuration (can be overridden by environment-specific vars)
jira_default_config:
  # Connection settings
  connection_timeout: 30
  read_timeout: 60
  retry_attempts: 3
  retry_delay: 5
  validate_certificates: false
  
  # API configuration
  api_version: "2"
  grid_api_version: "1.0"
  
  # Update behavior
  update_method: "grid_api"  # Options: grid_api, rest_api, comment
  update_field: "remark"
  update_format: "AAP Job: {job_url}"
  
  # Error handling
  ignore_connection_errors: true
  ignore_authentication_errors: false
  ignore_not_found_errors: true
  log_all_attempts: true
  
  # Notification settings
  notify_on_success: false
  notify_on_failure: true
  notify_on_retry: false

# =========================
# JIRA Instance Mapping
# =========================

# Environment-to-JIRA instance mapping
jira_environment_mapping:
  production:
    instance_name: "production"
    base_url: "https://itsm.hcloud.healthgrp.com.sg"
    project_key: "SR"
    environment_tag: "PROD"
  
  staging:
    instance_name: "uat"
    base_url: "https://jsd-uat.hcloud.healthgrp.com.sg"
    project_key: "SR"
    environment_tag: "STG"
  
  development:
    instance_name: "uat"
    base_url: "https://jsd-uat.hcloud.healthgrp.com.sg"
    project_key: "SR"
    environment_tag: "DEV"
  
  uat:
    instance_name: "uat"
    base_url: "https://jsd-uat.hcloud.healthgrp.com.sg"
    project_key: "SR"
    environment_tag: "UAT"

# =========================
# JIRA API Endpoints
# =========================

# API endpoint templates
jira_api_endpoints:
  # REST API endpoints
  rest_api:
    base: "{base_url}/rest/api/{api_version}"
    issue: "{base_url}/rest/api/{api_version}/issue/{issue_key}"
    comment: "{base_url}/rest/api/{api_version}/issue/{issue_key}/comment"
    transition: "{base_url}/rest/api/{api_version}/issue/{issue_key}/transitions"
  
  # Grid API endpoints (Idalko Grid)
  grid_api:
    base: "{base_url}/rest/idalko-grid/{grid_api_version}/api/grid"
    update: "{base_url}/rest/idalko-grid/{grid_api_version}/api/grid/{grid_id}/issue/{issue_key}/"
    
# =========================
# JIRA Update Templates
# =========================

# Update message templates
jira_update_templates:
  # Standard AAP job link format (v1 compatibility)
  aap_job_link:
    format: "{aap_url}/#/jobs/playbook/{job_id}/"
    description: "AAP Job Link"
  
  # Enhanced update with execution details
  enhanced_update:
    format: |
      DNS Automation Execution Details:
      - Job ID: {job_id}
      - Operation: {operation}
      - Domain: {domain}
      - Hostname: {hostname}
      - Status: {status}
      - Execution Time: {execution_time}
      - AAP Job: {aap_url}/#/jobs/playbook/{job_id}/
    description: "DNS Automation Execution Summary"
  
  # Simple status update
  status_update:
    format: "DNS automation {operation} completed for {hostname}.{domain} - Job: {job_id}"
    description: "DNS Operation Status"

# =========================
# JIRA Field Mapping
# =========================

# Field mapping for different update methods
jira_field_mapping:
  grid_api:
    remark: "remark"
    status: "status"
    assignee: "assignee"
    resolution: "resolution"
  
  rest_api:
    description: "description"
    comment: "comment"
    status: "status"
    assignee: "assignee"

# =========================
# JIRA Validation Rules
# =========================

# Validation rules for JIRA integration
jira_validation_rules:
  # Service request number validation
  sr_number:
    pattern: "^SR-\\d+$"
    required: true
    error_message: "Invalid service request number format. Expected: SR-XXXXXX"
  
  # Grid ID validation
  grid_id:
    pattern: "^\\d+$"
    required_for_grid_api: true
    error_message: "Grid ID must be numeric"
  
  # Row ID validation
  row_id:
    pattern: "^\\d+$"
    required_for_grid_api: true
    error_message: "Row ID must be numeric"

# =========================
# JIRA Error Handling
# =========================

# Error handling configuration
jira_error_handling:
  # HTTP status code handling
  status_codes:
    200: "success"
    201: "created"
    204: "updated"
    400: "bad_request"
    401: "unauthorized"
    403: "forbidden"
    404: "not_found"
    500: "server_error"
    502: "bad_gateway"
    503: "service_unavailable"
  
  # Retry conditions
  retry_on_status_codes:
    - 500
    - 502
    - 503
    - 504
  
  # Ignore conditions
  ignore_on_status_codes:
    - 404  # Ticket not found
  
  # Critical errors (always fail)
  critical_status_codes:
    - 401  # Authentication failure
    - 403  # Authorization failure

# =========================
# JIRA Integration Metrics
# =========================

# Metrics collection for JIRA integration
jira_metrics:
  # Performance metrics
  track_response_time: true
  track_retry_count: true
  track_success_rate: true
  
  # Error metrics
  track_error_types: true
  track_failure_reasons: true
  
  # Usage metrics
  track_update_frequency: true
  track_api_usage: true

# =========================
# JIRA Security Configuration
# =========================

# Security settings for JIRA integration
jira_security:
  # Credential management
  use_cyberark: true
  credential_rotation: true
  
  # API security
  use_service_account: true
  limit_api_permissions: true
  
  # Audit requirements
  log_all_api_calls: true
  mask_sensitive_data: true
  retain_audit_logs: true

# =========================
# JIRA Integration Testing
# =========================

# Testing configuration
jira_testing:
  # Test mode settings
  test_mode_enabled: false
  test_ticket_prefix: "TEST-"
  test_grid_id: "999999"
  test_row_id: "999999"
  
  # Mock responses
  mock_responses:
    enabled: false
    success_response: '{"status": "success", "message": "Ticket updated"}'
    error_response: '{"status": "error", "message": "Update failed"}'
  
  # Validation tests
  validate_connectivity: true
  validate_authentication: true
  validate_permissions: true
