# Infrastructure Architecture Design Task

## Purpose

- To design a comprehensive, scalable, and secure infrastructure architecture for on-premise environments
- To make definitive technology choices within the approved stack (Windows Servers, RHEL, Oracle Linux, VMware ESXi, Nutanix HCI, Red Hat OpenShift)
- To produce detailed architectural documentation that enables efficient implementation by infrastructure teams
- To ensure alignment with business requirements, security compliance, and operational excellence standards

## Instructions

1. **Requirements Analysis & Validation:**

   - Review infrastructure requirements document and business objectives
   - Analyze current infrastructure state and constraints
   - Identify integration requirements with existing systems
   - Validate compliance and security requirements
   - Document assumptions, dependencies, and constraints
   - Present requirements summary to stakeholder for confirmation

2. **Technology Stack Assessment:**

   - Evaluate current infrastructure capabilities and gaps
   - Assess technology stack alignment with approved platforms:
     - **Compute:** Windows Servers, RHEL, Oracle Linux
     - **Virtualization:** VMware ESXi, Nutanix HCI
     - **Container Platform:** Red Hat OpenShift
     - **Automation:** Ansible Automation Platform
   - Recommend specific versions and configurations
   - Consider licensing, support, and operational implications
   - Document technology choices and rationale

3. **High-Level Architecture Design:**

   - Create infrastructure topology and component overview
   - Design network architecture and segmentation strategy
   - Plan compute resource allocation and sizing
   - Design storage architecture and data management
   - Plan for high availability and disaster recovery
   - Create architectural diagrams using standard notation

4. **Detailed Component Design:**

   - **Compute Infrastructure:**
     - Server sizing and configuration specifications
     - Virtualization strategy and resource allocation
     - Operating system deployment and configuration
     - Performance optimization and tuning guidelines

   - **Network Architecture:**
     - Network topology and VLAN segmentation
     - Routing and switching configuration
     - Load balancing and traffic management
     - Network security and access controls

   - **Storage Design:**
     - Storage system architecture and capacity planning
     - Data protection and backup strategies
     - Performance optimization for different workloads
     - Data lifecycle management policies

   - **Container Platform (if applicable):**
     - Red Hat OpenShift cluster design and topology
     - Container orchestration and scheduling
     - Storage and networking for containerized workloads
     - Integration with existing infrastructure

5. **Security & Compliance Integration:**

   - Design security zones and network segmentation
   - Plan access controls and identity management
   - Integrate monitoring and audit capabilities
   - Address data protection and privacy requirements
   - Document security controls and compliance measures

6. **Capacity Planning & Scalability:**

   - Analyze current and projected workload requirements
   - Size compute, storage, and network resources
   - Plan for growth and scalability requirements
   - Consider performance and availability targets
   - Document capacity planning assumptions and calculations

7. **Operational Considerations:**

   - Design monitoring and alerting strategies
   - Plan for backup and disaster recovery procedures
   - Consider maintenance and update procedures
   - Design automation and configuration management integration
   - Plan for incident response and troubleshooting

8. **Documentation & Validation:**

   - Create comprehensive architecture documentation using template
   - Include detailed diagrams and specifications
   - Document design decisions and trade-offs
   - Validate design against requirements and constraints
   - Review with stakeholders and technical teams
   - Update documentation based on feedback

9. **Implementation Planning:**

   - Create implementation roadmap and phases
   - Identify dependencies and critical path items
   - Plan for testing and validation procedures
   - Consider migration strategies for existing workloads
   - Document implementation guidelines and procedures

10. **Quality Assurance & Checklist Validation:**

    - Review design against infrastructure design checklist
    - Validate compliance with organizational standards
    - Ensure documentation completeness and accuracy
    - Confirm stakeholder approval and sign-off
    - Plan for architecture review and update procedures

## Interaction Guidelines

- **Incremental Approach:** Present design sections for review and approval before proceeding
- **Stakeholder Engagement:** Regularly validate design decisions with business and technical stakeholders
- **Documentation Focus:** Maintain comprehensive documentation throughout the design process
- **Quality Gates:** Ensure each design phase meets quality standards before proceeding

## Output Deliverables

- **Infrastructure Architecture Document** (using infrastructure-architecture-tmpl.md)
- **Network Topology Diagrams** with detailed specifications
- **Capacity Planning Analysis** with sizing calculations
- **Security Architecture Design** with compliance mapping
- **Implementation Roadmap** with phases and dependencies
- **Operational Procedures** for monitoring and maintenance
- **Technology Stack Recommendations** with justification
- **Disaster Recovery Plan** with RTO/RPO specifications

## Quality Validation

- Architecture aligns with business requirements and technical constraints
- Technology choices are within approved stack and properly justified
- Capacity planning includes current and future requirements with growth projections
- Security and compliance requirements are comprehensively addressed
- Documentation is complete, accurate, and maintainable
- Design is reviewed and approved by all relevant stakeholders
- Implementation guidance is clear, detailed, and actionable
- Operational procedures are defined and documented

## Integration Points

- **Platform Engineer:** Container platform and CI/CD pipeline integration
- **Systems Engineer:** Server configuration and virtualization coordination
- **Security Engineer:** Security requirements and compliance validation
- **Automation Engineer:** Infrastructure automation and configuration management
- **DevOps Engineer:** Monitoring, deployment, and operational alignment
