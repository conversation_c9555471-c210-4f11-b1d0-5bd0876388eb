# AAP Extra Variables - YAML Reference Guide

## Quick Reference for DNS Management Automation v2

**Framework:** Operational Excellence Automation Framework (OXAF)
**Version:** 2.0
**Purpose:** Comprehensive YAML examples for AAP execution

---

## 🚀 **Quick Start YAML Templates**

### Copy-Paste Ready Templates

#### **DNS Record Addition (Production)**
```yaml
dns_action: add
domain: healthgrp.com.sg
hostname: YOUR_HOSTNAME
ipaddress: YOUR_IP_ADDRESS
ttl: 3600
var_sr_number: YOUR_TICKET_NUMBER  # SR-XXXXXX, SCR-XXXXXX, or INC-XXXXXX
testing_mode: false
```

#### **DNS Record Verification**
```yaml
dns_action: verify
domain: healthgrp.com.sg
hostname: YOUR_HOSTNAME
var_sr_number: YOUR_TICKET_NUMBER  # SR-XXXXXX, SCR-XXXXXX, or INC-XXXXXX
testing_mode: false
```

#### **DNS Record Removal**
```yaml
dns_action: remove
domain: healthgrp.com.sg
hostname: YOUR_HOSTNAME
var_sr_number: YOUR_SR_NUMBER
testing_mode: false
```

#### **DNS Record Update**
```yaml
dns_action: update
domain: healthgrp.com.sg
hostname: YOUR_HOSTNAME
ipaddress: YOUR_NEW_IP_ADDRESS
ttl: 3600
var_sr_number: YOUR_SR_NUMBER
testing_mode: false
```

#### **DNS Record Synchronization**
```yaml
dns_action: sync
domain: healthgrp.com.sg
hostname: YOUR_HOSTNAME
var_sr_number: YOUR_SR_NUMBER
testing_mode: false
```

---

## 📋 **Variable Reference Table**

| Variable | Type | Required | Default | Description |
|----------|------|----------|---------|-------------|
| `action` | String | ✅ Yes | N/A | DNS operation: verify, add, remove, update, sync |
| `domain` | String | ✅ Yes | N/A | Target DNS domain (e.g., healthgrp.com.sg) |
| `hostname` | String | ✅ Yes | N/A | Target hostname (without domain) |
| `ipaddress` | String | ⚠️ Conditional | N/A | Required for add/update operations |
| `ttl` | Integer | ❌ No | 3600 | Time-to-live in seconds |
| `var_sr_number` | String | ✅ Yes | N/A | JIRA ticket number for tracking (SR-XXXXXX, SCR-XXXXXX, or INC-XXXXXX) |
| `testing_mode` | Boolean | ❌ No | false | Enable dry-run mode |
| `environment` | String | ❌ No | production | Target environment override |

---

## 🎫 **JIRA Ticket Type Examples**

### **Service Request (SR) - Standard DNS Operations**
```yaml
dns_action: add
domain: healthgrp.com.sg
hostname: webserver01
ipaddress: ************0
ttl: 3600
var_sr_number: SR-123456
testing_mode: false
```

### **Service Change Request (SCR) - Infrastructure Changes**
```yaml
dns_action: update
domain: healthgrp.com.sg
hostname: dbserver01
ipaddress: ************
ttl: 7200
var_sr_number: SCR-789012
testing_mode: false
```

### **Incident (INC) - Emergency DNS Fixes**
```yaml
dns_action: remove
domain: healthgrp.com.sg
hostname: compromised-server
var_sr_number: INC-345678
testing_mode: false
```

---

## 🌍 **Environment-Specific Examples**

### **Development Environment**
```yaml
dns_action: add
domain: devhealthgrp.com.sg
hostname: devserver01
ipaddress: **********
ttl: 300
dns_environment: development
testing_mode: true
var_sr_number: SR-DEV-001
```

### **Staging Environment**
```yaml
dns_action: add
domain: stghealthgrp.com.sg
hostname: stgserver01
ipaddress: ***************
ttl: 1800
dns_environment: staging
testing_mode: false
var_sr_number: SR-STG-001
```

### **Production Environment**
```yaml
dns_action: add
domain: healthgrp.com.sg
hostname: prodserver01
ipaddress: ************
ttl: 3600
dns_environment: production
testing_mode: false
var_sr_number: SR-PROD-001
```

---

## 🔧 **Advanced Configuration Examples**

### **Enhanced Logging and Audit**
```yaml
dns_action: add
domain: healthgrp.com.sg
hostname: auditserver01
ipaddress: ************0
ttl: 3600
enhanced_logging: true
audit_level: detailed
var_sr_number: SR-AUDIT-001
testing_mode: false
```

### **Rollback Enabled Operation**
```yaml
dns_action: update
domain: healthgrp.com.sg
hostname: criticalserver01
ipaddress: *************
ttl: 3600
enable_rollback: true
rollback_timeout: 300
var_sr_number: SR-CRITICAL-001
testing_mode: false
```

### **Validation Before Execution**
```yaml
dns_action: add
domain: healthgrp.com.sg
hostname: validatedserver01
ipaddress: *************
ttl: 3600
validate_before_execution: true
var_sr_number: SR-VALIDATE-001
testing_mode: false
```

---

## 📊 **Common Use Case Examples**

### **Web Server Deployment**
```yaml
dns_action: add
domain: healthgrp.com.sg
hostname: web01
ipaddress: *************
ttl: 3600
var_sr_number: SR-WEB-001
testing_mode: false
execution_context:
  purpose: Web server deployment
  team: Infrastructure Team
  operator: <EMAIL>
```

### **Database Server Migration**
```yaml
dns_action: update
domain: healthgrp.com.sg
hostname: db01
ipaddress: ************
ttl: 7200
var_sr_number: SR-DB-MIGRATE-001
testing_mode: false
execution_context:
  purpose: Database server migration
  maintenance_window: "2024-01-15 02:00:00 SGT"
  approval_reference: CHANGE-2024-001
```

### **Server Decommissioning**
```yaml
dns_action: remove
domain: healthgrp.com.sg
hostname: legacy01
var_sr_number: SR-DECOM-001
testing_mode: false
execution_context:
  purpose: Server decommissioning
  approval_reference: CHANGE-2024-002
  decommission_date: "2024-01-20"
```

### **Load Balancer Configuration**
```yaml
dns_action: add
domain: healthgrp.com.sg
hostname: lb01
ipaddress: ************
ttl: 300
var_sr_number: SR-LB-001
testing_mode: false
execution_context:
  purpose: Load balancer setup
  service_type: critical
  health_check_required: true
```

---

## 🔄 **Post-Zone Creation Scenarios**

### **Zone Created by DNS Engineers**
```yaml
dns_action: sync
domain: newapp.healthgrp.com.sg
hostname: app01
ipaddress: *************
ttl: 3600
var_sr_number: SR-ZONE-SYNC-001
testing_mode: false
execution_context:
  purpose: Post-zone creation synchronization
  zone_created_by: DNS Engineering Team
  zone_creation_ticket: TASK-DNS-001
```

### **Missing PTR Record Fix**
```yaml
dns_action: sync
domain: healthgrp.com.sg
hostname: fixptr01
var_sr_number: SR-PTR-FIX-001
testing_mode: false
execution_context:
  purpose: Fix missing PTR record
  issue_reference: INC-2024-001
  discovered_by: monitoring_system
```

---

## 🧪 **Testing and Validation Examples**

### **Dry Run Testing**
```yaml
dns_action: add
domain: healthgrp.com.sg
hostname: testserver01
ipaddress: *************
ttl: 300
testing_mode: true
var_sr_number: SR-TEST-001
execution_context:
  purpose: Testing DNS automation
  tester: <EMAIL>
  test_phase: validation
```

### **Development Testing**
```yaml
dns_action: add
domain: devhealthgrp.com.sg
hostname: unittest01
ipaddress: ***********
ttl: 60
dns_environment: development
testing_mode: true
var_sr_number: SR-UNITTEST-001
execution_context:
  purpose: Unit testing
  test_suite: dns_automation_v2
```

---

## 📝 **YAML Best Practices**

### **Formatting Guidelines**
- Use 2 spaces for indentation (not tabs)
- Align nested elements consistently
- Use quotes for strings containing special characters
- Keep boolean values as true/false (lowercase)

### **Good YAML Example**
```yaml
dns_action: add
domain: healthgrp.com.sg
hostname: server01
ipaddress: ************0
ttl: 3600
testing_mode: false
var_sr_number: SR-123456
execution_context:
  purpose: Server deployment
  team: Infrastructure
```

### **Common YAML Mistakes to Avoid**
```yaml
# ❌ Bad - Inconsistent indentation
dns_action: add
  domain: healthgrp.com.sg
    hostname: server01

# ❌ Bad - Using tabs instead of spaces
dns_action: add
	domain: healthgrp.com.sg

# ❌ Bad - Incorrect boolean values
testing_mode: True  # Should be: true

# ❌ Bad - Missing quotes for special characters
hostname: server-01:8080  # Should be: "server-01:8080"
```

---

## 🔄 **Bulk Operations with YAML**

### **Multiple Server Addition**
```yaml
dns_action: add
ttl: 3600
var_sr_number: SR-BULK-001
testing_mode: false
servers:
  - domain: healthgrp.com.sg
    hostname: web01
    ipaddress: *************
  - domain: healthgrp.com.sg
    hostname: web02
    ipaddress: ************2
  - domain: healthgrp.com.sg
    hostname: web03
    ipaddress: ************3
execution_context:
  purpose: Web farm deployment
  deployment_batch: batch_1
```

### **Multi-Environment Deployment**
```yaml
dns_action: add
var_sr_number: SR-MULTI-ENV-001
testing_mode: false
environments:
  development:
    domain: devhealthgrp.com.sg
    hostname: api01
    ipaddress: **********
    ttl: 300
  staging:
    domain: stghealthgrp.com.sg
    hostname: api01
    ipaddress: ***************
    ttl: 1800
  production:
    domain: healthgrp.com.sg
    hostname: api01
    ipaddress: ************
    ttl: 3600
```

---

## 📋 **YAML Comments and Documentation**

### **Using Comments for Documentation**
```yaml
# DNS Record Addition for Production Web Server
dns_action: add
domain: healthgrp.com.sg
hostname: web01
ipaddress: *************
ttl: 3600  # Standard production TTL
var_sr_number: SR-WEB-001
testing_mode: false  # Production deployment

# Execution context for audit trail
execution_context:
  purpose: Web server deployment
  team: Infrastructure Team
  operator: <EMAIL>
  deployment_date: "2024-01-15"

  # Change management references
  approval_reference: CHANGE-2024-001
  maintenance_window: "2024-01-15 02:00:00 SGT"

  # Technical details
  server_role: web_frontend
  application: customer_portal
  criticality: high
```

### **Template with Placeholders**
```yaml
# DNS Record Template - Replace placeholders before use
dns_action: add  # Change to: verify, add, remove, update, sync
domain: healthgrp.com.sg  # Change for different environments
hostname: REPLACE_WITH_HOSTNAME
ipaddress: REPLACE_WITH_IP_ADDRESS
ttl: 3600  # Adjust based on requirements
var_sr_number: REPLACE_WITH_SR_NUMBER
testing_mode: false  # Set to true for testing

# Optional execution context
execution_context:
  purpose: REPLACE_WITH_PURPOSE
  team: REPLACE_WITH_TEAM_NAME
  operator: REPLACE_WITH_OPERATOR_EMAIL
```

---

## 🎯 **Quick Copy Templates by Operation**

### **One-Line YAML Templates**

#### **VERIFY Operation**
```yaml
dns_action: verify
domain: healthgrp.com.sg
hostname: HOSTNAME
var_sr_number: SR-NUMBER
testing_mode: false
```

#### **ADD Operation**
```yaml
dns_action: add
domain: healthgrp.com.sg
hostname: HOSTNAME
ipaddress: IP_ADDRESS
ttl: 3600
var_sr_number: SR-NUMBER
testing_mode: false
```

#### **REMOVE Operation**
```yaml
dns_action: remove
domain: healthgrp.com.sg
hostname: HOSTNAME
var_sr_number: SR-NUMBER
testing_mode: false
```

#### **UPDATE Operation**
```yaml
dns_action: update
domain: healthgrp.com.sg
hostname: HOSTNAME
ipaddress: NEW_IP_ADDRESS
ttl: 3600
var_sr_number: SR-NUMBER
testing_mode: false
```

#### **SYNC Operation**
```yaml
dns_action: sync
domain: healthgrp.com.sg
hostname: HOSTNAME
var_sr_number: SR-NUMBER
testing_mode: false
```

---

## 🔍 **YAML Validation and Tools**

### **YAML Validation Checklist**
- ✅ Consistent indentation (2 spaces)
- ✅ No tabs used for indentation
- ✅ Proper boolean values (true/false)
- ✅ Quoted strings with special characters
- ✅ Required fields present for operation type
- ✅ Valid IP address format
- ✅ Proper domain format

### **Online YAML Validators**
- **YAML Lint:** http://www.yamllint.com/
- **Online YAML Parser:** https://yaml-online-parser.appspot.com/
- **YAML Validator:** https://codebeautify.org/yaml-validator

### **Command Line Validation**
```bash
# Using Python
python -c "import yaml; yaml.safe_load(open('variables.yaml'))"

# Using yq (if installed)
yq eval '.' variables.yaml

# Using Ansible (if available)
ansible-playbook --syntax-check -e @variables.yaml playbook.yml
```

---

## 📞 **Support and Troubleshooting**

### **Common YAML Issues**
1. **Indentation Error:** Use exactly 2 spaces, no tabs
2. **Boolean Format:** Use true/false (lowercase)
3. **String Quoting:** Quote strings with special characters
4. **Missing Required Fields:** Check operation-specific requirements

### **YAML vs JSON Decision Matrix**

| Scenario | Recommended Format | Reason |
|----------|-------------------|---------|
| Simple operations | JSON | More compact |
| Complex configurations | YAML | Better readability |
| Team collaboration | YAML | Comments and documentation |
| API integration | JSON | Universal support |
| Ansible-native workflows | YAML | Native format |
| Automated generation | JSON | Easier programmatic creation |

### **Support Contacts**
- **DNS Automation Team:** <EMAIL>
- **Infrastructure Team:** <EMAIL>
- **Emergency Support:** Available 24/7 through ITSM

---

*This YAML reference guide is part of the DNS Management Automation v2 solution developed by the CES Operational Excellence Team following Operational Excellence Automation Framework (OXAF) patterns.*
