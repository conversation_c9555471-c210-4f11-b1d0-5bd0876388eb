# Troubleshooting Guide - DNS Management Automation v2

## Comprehensive Problem Resolution Guide

**Framework:** Operational Excellence Automation Framework (OXAF)  
**Version:** 2.0  
**Purpose:** Complete troubleshooting guide for DNS Management Automation v2  

---

## 🎯 **Overview**

This guide provides comprehensive troubleshooting procedures for the DNS Management Automation v2 solution. It covers common issues, diagnostic procedures, and resolution steps for all components of the system.

### **Troubleshooting Categories:**
- ✅ **DNS Operation Issues** - Record management problems
- ✅ **JIRA Integration Issues** - Service request integration problems
- ✅ **CyberArk Integration Issues** - Credential retrieval problems
- ✅ **Network Connectivity Issues** - Communication problems
- ✅ **Authentication Issues** - Access and permission problems
- ✅ **Performance Issues** - Slow execution and timeouts

---

## 🔧 **DNS Operation Issues**

### **Issue 1: DNS Record Not Found**

#### **Symptoms:**
```
Error: DNS record 'hostname.domain.com' not found
Status: FAILED
Operation: verify/remove
```

#### **Diagnostic Steps:**
```powershell
# Check if DNS zone exists
Get-DnsServerZone -Name "domain.com" -ComputerName "dns-server"

# Check DNS server connectivity
Test-NetConnection -ComputerName "dns-server" -Port 53

# Verify DNS record manually
nslookup hostname.domain.com dns-server
```

#### **Resolution:**
1. **Verify Zone Exists:** Ensure the DNS zone is configured on the target server
2. **Check Record Name:** Verify hostname spelling and domain format
3. **Confirm DNS Server:** Ensure correct DNS server is being targeted
4. **Check Permissions:** Verify service account has read access to DNS zone

### **Issue 2: DNS Record Creation Failed**

#### **Symptoms:**
```
Error: Failed to create DNS record
Exception: Access denied or record already exists
Operation: add
```

#### **Diagnostic Steps:**
```powershell
# Check if record already exists
Get-DnsServerResourceRecord -ZoneName "domain.com" -Name "hostname" -RRType A

# Verify DNS server permissions
Get-DnsServerZone -Name "domain.com" | Select-Object -ExpandProperty SecureSecondaries

# Test DNS server write access
Add-DnsServerResourceRecordA -ZoneName "test.domain.com" -Name "test" -IPv4Address "***********" -WhatIf
```

#### **Resolution:**
1. **Check Existing Records:** Remove conflicting records if necessary
2. **Verify Permissions:** Ensure service account has write access to DNS zone
3. **Validate IP Address:** Confirm IP address format and availability
4. **Check Zone Configuration:** Verify zone allows dynamic updates

### **Issue 3: PTR Record Creation Failed**

#### **Symptoms:**
```
Warning: PTR record creation failed
Error: Reverse zone not found
Zone: 1.168.192.in-addr.arpa
```

#### **Diagnostic Steps:**
```powershell
# Check reverse zone existence
Get-DnsServerZone -Name "1.168.192.in-addr.arpa" -ComputerName "dns-server"

# List all reverse zones
Get-DnsServerZone | Where-Object {$_.ZoneName -like "*.in-addr.arpa"}

# Check PTR record manually
nslookup *************
```

#### **Resolution:**
1. **Create Reverse Zone:** Create missing reverse DNS zone
2. **Configure Zone Delegation:** Set up proper zone delegation
3. **Update Zone Permissions:** Ensure service account can create PTR records
4. **Verify Network Configuration:** Confirm reverse DNS is properly configured

---

## 🎫 **JIRA Integration Issues**

### **Issue 4: JIRA Ticket Update Failed**

#### **Symptoms:**
```
Error: Failed to update JIRA ticket SR-123456
HTTP Status: 401 Unauthorized
API: Grid API and REST API both failed
```

#### **Diagnostic Steps:**
```bash
# Test JIRA connectivity
curl -X GET "https://itsm.hcloud.healthgrp.com.sg/rest/api/2/myself" \
  -H "Authorization: Basic $(echo -n 'username:password' | base64)"

# Test specific ticket access
curl -X GET "https://itsm.hcloud.healthgrp.com.sg/rest/api/2/issue/SR-123456" \
  -H "Authorization: Basic $(echo -n 'username:password' | base64)"

# Verify Grid API token
curl -X GET "https://itsm.hcloud.healthgrp.com.sg/rest/idalko-grid/1.0/api/grid" \
  -H "Authorization: Bearer grid_token"
```

#### **Resolution:**
1. **Verify Credentials:** Check CyberArk credential retrieval
2. **Test Ticket Access:** Confirm ticket exists and is accessible
3. **Check API Permissions:** Verify service account has JIRA API access
4. **Validate Grid Configuration:** Ensure Grid API token is valid

### **Issue 5: Invalid JIRA Ticket Format**

#### **Symptoms:**
```
Error: Invalid JIRA ticket number format
Expected: SR-XXXXXX, SCR-XXXXXX, or INC-XXXXXX
Provided: REQ-123456
```

#### **Diagnostic Steps:**
```bash
# Test ticket format validation
echo "REQ-123456" | grep -E "^(SR|SCR|INC)-[0-9]+$"

# Check supported ticket types
grep -r "supported_types" group_vars/all/jira_config.yml
```

#### **Resolution:**
1. **Use Correct Format:** Ensure ticket follows SR-XXXXXX, SCR-XXXXXX, or INC-XXXXXX format
2. **Check Ticket Type:** Verify ticket type is supported (SR, SCR, INC)
3. **Validate Ticket Number:** Ensure ticket number is numeric
4. **Update Configuration:** Add new ticket types if required

---

## 🔐 **CyberArk Integration Issues**

### **Issue 6: CyberArk Credential Retrieval Failed**

#### **Symptoms:**
```
Error: Failed to retrieve credentials from CyberArk
Account: DNS_SERVICE_ACCOUNT
Message: Account not found or access denied
```

#### **Diagnostic Steps:**
```yaml
# Test CyberArk connectivity
- name: "Test CyberArk Connection"
  cloud_cpe.cyberark_ccp.cyberark_credential:
    account: "DNS_SERVICE_ACCOUNT"
  register: test_result
  ignore_errors: true

- name: "Display CyberArk Error"
  debug:
    var: test_result
  when: test_result is failed
```

#### **Resolution:**
1. **Verify Account Exists:** Check account exists in CyberArk safe
2. **Check Permissions:** Ensure AAP has access to CyberArk safe
3. **Validate Account Name:** Confirm account name spelling is correct
4. **Test Connectivity:** Verify network connectivity to CyberArk

### **Issue 7: CyberArk Collection Not Found**

#### **Symptoms:**
```
Error: Collection 'cloud_cpe.cyberark_ccp' not found
Module: cyberark_credential
```

#### **Diagnostic Steps:**
```bash
# Check collection installation
ansible-galaxy collection list | grep cyberark

# Verify collection requirements
cat collections/requirements.yml

# Test collection installation
ansible-galaxy collection install -r collections/requirements.yml
```

#### **Resolution:**
1. **Install Collection:** Run `ansible-galaxy collection install -r collections/requirements.yml`
2. **Verify Installation:** Check collection is properly installed
3. **Update Requirements:** Ensure requirements.yml includes CyberArk collection
4. **Check Permissions:** Verify AAP can install collections

---

## 🌐 **Network Connectivity Issues**

### **Issue 8: DNS Server Unreachable**

#### **Symptoms:**
```
Error: Unable to connect to DNS server
Server: dns-prod-01.healthgrp.com.sg
Port: 53 (DNS) and 5985 (WinRM)
```

#### **Diagnostic Steps:**
```bash
# Test DNS port connectivity
nc -zv dns-prod-01.healthgrp.com.sg 53

# Test WinRM connectivity
nc -zv dns-prod-01.healthgrp.com.sg 5985

# Test name resolution
nslookup dns-prod-01.healthgrp.com.sg

# Test ping connectivity
ping dns-prod-01.healthgrp.com.sg
```

#### **Resolution:**
1. **Check Network Connectivity:** Verify network path to DNS server
2. **Verify Firewall Rules:** Ensure ports 53 and 5985 are open
3. **Check DNS Resolution:** Confirm DNS server name resolves correctly
4. **Validate Server Status:** Ensure DNS server is running and accessible

### **Issue 9: JIRA API Unreachable**

#### **Symptoms:**
```
Error: Connection timeout to JIRA API
URL: https://itsm.hcloud.healthgrp.com.sg/rest/api/2
Timeout: 30 seconds
```

#### **Diagnostic Steps:**
```bash
# Test HTTPS connectivity
curl -I https://itsm.hcloud.healthgrp.com.sg

# Test specific API endpoint
curl -X GET https://itsm.hcloud.healthgrp.com.sg/rest/api/2/serverInfo

# Check DNS resolution
nslookup itsm.hcloud.healthgrp.com.sg

# Test network path
traceroute itsm.hcloud.healthgrp.com.sg
```

#### **Resolution:**
1. **Check Internet Connectivity:** Verify outbound internet access
2. **Validate SSL Certificates:** Ensure SSL certificates are valid
3. **Check Proxy Settings:** Configure proxy if required
4. **Verify JIRA Status:** Confirm JIRA service is operational

---

## 🔑 **Authentication Issues**

### **Issue 10: Windows Authentication Failed**

#### **Symptoms:**
```
Error: Authentication failed for DNS server
User: service_account
Server: dns-prod-01.healthgrp.com.sg
Protocol: Kerberos/NTLM
```

#### **Diagnostic Steps:**
```powershell
# Test credential validation
$cred = Get-Credential
Test-WSMan -ComputerName "dns-prod-01.healthgrp.com.sg" -Credential $cred

# Check domain membership
Get-ComputerInfo | Select-Object CsDomain, CsDomainRole

# Verify service account
Get-ADUser -Identity "service_account" -Properties *
```

#### **Resolution:**
1. **Verify Credentials:** Check username and password are correct
2. **Check Account Status:** Ensure service account is not locked or expired
3. **Validate Permissions:** Confirm account has logon rights to DNS server
4. **Test Authentication:** Use different authentication methods (NTLM/Kerberos)

### **Issue 11: JIRA Authentication Failed**

#### **Symptoms:**
```
Error: JIRA authentication failed
HTTP Status: 401 Unauthorized
User: jira_service_account
```

#### **Diagnostic Steps:**
```bash
# Test basic authentication
curl -X GET "https://itsm.hcloud.healthgrp.com.sg/rest/api/2/myself" \
  -H "Authorization: Basic $(echo -n 'username:password' | base64)" \
  -v

# Check account status in JIRA
# (Manual check through JIRA web interface)

# Verify API permissions
curl -X GET "https://itsm.hcloud.healthgrp.com.sg/rest/api/2/permissions" \
  -H "Authorization: Basic $(echo -n 'username:password' | base64)"
```

#### **Resolution:**
1. **Verify JIRA Credentials:** Check username and password in CyberArk
2. **Check Account Permissions:** Ensure account has API access rights
3. **Validate Account Status:** Confirm account is active and not locked
4. **Test API Access:** Verify account can access required JIRA projects

---

## ⏱️ **Performance Issues**

### **Issue 12: Slow DNS Operations**

#### **Symptoms:**
```
Warning: DNS operation taking longer than expected
Operation: add
Duration: 180 seconds (expected < 30 seconds)
```

#### **Diagnostic Steps:**
```powershell
# Measure DNS query performance
Measure-Command { Get-DnsServerResourceRecord -ZoneName "domain.com" -Name "hostname" }

# Check DNS server performance
Get-Counter "\DNS\Total Query Received/sec"

# Monitor network latency
Test-NetConnection -ComputerName "dns-server" -DiagnoseRouting
```

#### **Resolution:**
1. **Check DNS Server Load:** Monitor DNS server CPU and memory usage
2. **Optimize Network Path:** Reduce network latency to DNS server
3. **Tune Timeout Settings:** Adjust timeout values in configuration
4. **Consider Load Balancing:** Distribute load across multiple DNS servers

### **Issue 13: JIRA API Timeouts**

#### **Symptoms:**
```
Error: JIRA API request timeout
URL: https://itsm.hcloud.healthgrp.com.sg/rest/api/2/issue/SR-123456/comment
Timeout: 30 seconds
```

#### **Diagnostic Steps:**
```bash
# Measure API response time
time curl -X GET "https://itsm.hcloud.healthgrp.com.sg/rest/api/2/myself"

# Check JIRA server status
curl -X GET "https://itsm.hcloud.healthgrp.com.sg/status"

# Monitor network performance
ping -c 10 itsm.hcloud.healthgrp.com.sg
```

#### **Resolution:**
1. **Increase Timeout Values:** Adjust API timeout settings
2. **Check JIRA Performance:** Monitor JIRA server performance
3. **Optimize API Calls:** Reduce payload size and frequency
4. **Implement Retry Logic:** Add exponential backoff for retries

---

## 🔍 **Diagnostic Commands**

### **System Health Check:**
```bash
# Complete system health check
ansible-playbook main.yml --tags="validation" --check

# Test all integrations
ansible-playbook main.yml -e "action=verify domain=test.com hostname=test var_sr_number=SR-TEST-001"

# Check configuration syntax
ansible-playbook main.yml --syntax-check

# Validate inventory
ansible-inventory --list
```

### **Component-Specific Diagnostics:**
```bash
# DNS-specific diagnostics
ansible-playbook main.yml --tags="dns,validation"

# JIRA-specific diagnostics
ansible-playbook main.yml --tags="jira,validation"

# CyberArk-specific diagnostics
ansible-playbook main.yml --tags="cyberark,validation"

# Network connectivity tests
ansible all -m ping
```

### **Log Analysis:**
```bash
# Check Ansible logs
tail -f /var/log/ansible/ansible.log

# Check AAP job logs
# (Access through AAP web interface)

# Check system logs
journalctl -u ansible-automation-platform

# Check DNS server logs
# (Access through Windows Event Viewer on DNS servers)
```

---

## 📞 **Support and Escalation**

### **Support Levels:**

#### **Level 1 - Self-Service:**
- Use this troubleshooting guide
- Check documentation and user guides
- Review configuration settings
- Test with validation commands

#### **Level 2 - Team Support:**
- **DNS Issues:** <EMAIL>
- **JIRA Issues:** <EMAIL>
- **CyberArk Issues:** <EMAIL>
- **Network Issues:** <EMAIL>

#### **Level 3 - Escalation:**
- **Critical Issues:** <EMAIL>
- **Security Issues:** <EMAIL>
- **Emergency Support:** Available 24/7 through ITSM

### **Information to Provide:**
- **Error Messages:** Complete error text and stack traces
- **Environment:** Production, Staging, or Development
- **Operation:** Specific DNS operation being performed
- **Ticket Number:** JIRA ticket number if applicable
- **Logs:** Relevant log files and diagnostic output
- **Timeline:** When the issue started and frequency

---

## 📋 **Common Resolution Checklist**

### **Before Contacting Support:**
- ✅ **Check Documentation:** Review user guide and configuration guide
- ✅ **Verify Configuration:** Ensure all settings are correct
- ✅ **Test Connectivity:** Confirm network connectivity to all services
- ✅ **Check Credentials:** Verify all credentials are valid and accessible
- ✅ **Review Logs:** Check logs for specific error messages
- ✅ **Test Components:** Use diagnostic commands to isolate issues

### **Information Gathering:**
- ✅ **Error Details:** Complete error messages and codes
- ✅ **Environment Info:** Which environment is affected
- ✅ **Recent Changes:** Any recent configuration or system changes
- ✅ **Impact Assessment:** How many users/systems are affected
- ✅ **Workarounds:** Any temporary workarounds being used

---

*This troubleshooting guide is part of the DNS Management Automation v2 solution developed by the CES Operational Excellence Team following Operational Excellence Automation Framework (OXAF) patterns.*
